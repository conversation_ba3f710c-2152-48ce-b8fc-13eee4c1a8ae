"""
Écran des rapports et statistiques
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.tab import MDTabs, MDTabsBase
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.app import MDApp
from kivy.clock import Clock
from kivy.garden.matplotlib.backend_kivyagg import FigureCanvasKivyAgg
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import threading


class Tab(MDFloatLayout, MDTabsBase):
    """Classe de base pour les onglets"""
    pass


class StatisticCard(MDCard):
    """Carte pour afficher une statistique"""
    
    def __init__(self, title, value, subtitle="", icon="chart-line", color=None, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "120dp"
        
        if color:
            self.md_bg_color = color
        
        layout = MDBoxLayout(orientation='vertical', spacing="8dp")
        
        # En-tête avec titre et icône
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        title_label = MDLabel(
            text=title,
            font_style="Subtitle1",
            theme_text_color="Primary" if not color else "Custom",
            text_color=(1, 1, 1, 1) if color else None,
            size_hint_x=0.8
        )
        
        icon_button = MDIconButton(
            icon=icon,
            theme_icon_color="Primary" if not color else "Custom",
            icon_color=(1, 1, 1, 1) if color else None,
            size_hint_x=0.2
        )
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(icon_button)
        
        # Valeur principale
        value_label = MDLabel(
            text=str(value),
            font_style="H4",
            theme_text_color="Primary" if not color else "Custom",
            text_color=(1, 1, 1, 1) if color else None,
            halign="center"
        )
        
        # Sous-titre
        if subtitle:
            subtitle_label = MDLabel(
                text=subtitle,
                font_style="Caption",
                theme_text_color="Secondary" if not color else "Custom",
                text_color=(0.8, 0.8, 0.8, 1) if color else None,
                halign="center",
                size_hint_y=None,
                height="16dp"
            )
            layout.add_widget(header_layout)
            layout.add_widget(value_label)
            layout.add_widget(subtitle_label)
        else:
            layout.add_widget(header_layout)
            layout.add_widget(value_label)
        
        self.add_widget(layout)


class ChartCard(MDCard):
    """Carte pour afficher un graphique"""
    
    def __init__(self, title, chart_widget, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "400dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="8dp")
        
        # Titre
        title_label = MDLabel(
            text=title,
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        layout.add_widget(title_label)
        layout.add_widget(chart_widget)
        
        self.add_widget(layout)


class ReportsScreen(MDScreen):
    """Écran des rapports et statistiques"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.stats_data = {}
        self.build_ui()
    
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # Titre
        title_label = MDLabel(
            text="Rapports et Statistiques",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_y=None,
            height="48dp"
        )
        main_layout.add_widget(title_label)
        
        # Onglets pour différents types de rapports
        self.tabs = MDTabs()
        
        # Onglet Vue d'ensemble
        overview_tab = Tab(title="Vue d'ensemble")
        overview_content = self.create_overview_content()
        overview_tab.add_widget(overview_content)
        self.tabs.add_widget(overview_tab)
        
        # Onglet Ventes
        sales_tab = Tab(title="Ventes")
        sales_content = self.create_sales_content()
        sales_tab.add_widget(sales_content)
        self.tabs.add_widget(sales_tab)
        
        # Onglet Produits
        products_tab = Tab(title="Produits")
        products_content = self.create_products_content()
        products_tab.add_widget(products_content)
        self.tabs.add_widget(products_tab)
        
        # Onglet Clients
        clients_tab = Tab(title="Clients")
        clients_content = self.create_clients_content()
        clients_tab.add_widget(clients_content)
        self.tabs.add_widget(clients_tab)
        
        main_layout.add_widget(self.tabs)
        self.add_widget(main_layout)
    
    def create_overview_content(self):
        """Créer le contenu de l'onglet vue d'ensemble"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Période de sélection
        period_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp", spacing="8dp")
        
        period_buttons = [
            ("Aujourd'hui", self.load_today_stats),
            ("Cette semaine", self.load_week_stats),
            ("Ce mois", self.load_month_stats),
            ("Cette année", self.load_year_stats)
        ]
        
        for text, callback in period_buttons:
            btn = MDRaisedButton(
                text=text,
                size_hint_x=0.25,
                on_release=callback
            )
            period_layout.add_widget(btn)
        
        content.add_widget(period_layout)
        
        # Grille des statistiques principales
        self.overview_stats_grid = MDGridLayout(cols=2, spacing="8dp", adaptive_height=True)
        content.add_widget(self.overview_stats_grid)
        
        # Graphique des ventes
        self.overview_chart_container = MDBoxLayout(orientation='vertical', adaptive_height=True)
        content.add_widget(self.overview_chart_container)
        
        scroll.add_widget(content)
        return scroll
    
    def create_sales_content(self):
        """Créer le contenu de l'onglet ventes"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Statistiques des ventes
        self.sales_stats_grid = MDGridLayout(cols=2, spacing="8dp", adaptive_height=True)
        content.add_widget(self.sales_stats_grid)
        
        # Graphiques des ventes
        self.sales_chart_container = MDBoxLayout(orientation='vertical', adaptive_height=True)
        content.add_widget(self.sales_chart_container)
        
        scroll.add_widget(content)
        return scroll
    
    def create_products_content(self):
        """Créer le contenu de l'onglet produits"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Statistiques des produits
        self.products_stats_grid = MDGridLayout(cols=2, spacing="8dp", adaptive_height=True)
        content.add_widget(self.products_stats_grid)
        
        # Top des produits
        top_products_label = MDLabel(
            text="Top 10 des produits les plus vendus",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        content.add_widget(top_products_label)
        
        self.top_products_container = MDBoxLayout(orientation='vertical', adaptive_height=True)
        content.add_widget(self.top_products_container)
        
        scroll.add_widget(content)
        return scroll
    
    def create_clients_content(self):
        """Créer le contenu de l'onglet clients"""
        scroll = MDScrollView()
        content = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Statistiques des clients
        self.clients_stats_grid = MDGridLayout(cols=2, spacing="8dp", adaptive_height=True)
        content.add_widget(self.clients_stats_grid)
        
        # Top des clients
        top_clients_label = MDLabel(
            text="Top 10 des meilleurs clients",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        content.add_widget(top_clients_label)
        
        self.top_clients_container = MDBoxLayout(orientation='vertical', adaptive_height=True)
        content.add_widget(self.top_clients_container)
        
        scroll.add_widget(content)
        return scroll
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        self.load_month_stats()  # Charger les stats du mois par défaut
    
    def load_today_stats(self, *args):
        """Charger les statistiques du jour"""
        today = datetime.now().strftime('%Y-%m-%d')
        self.load_stats_for_period(today, today, "Aujourd'hui")
    
    def load_week_stats(self, *args):
        """Charger les statistiques de la semaine"""
        today = datetime.now()
        start_week = (today - timedelta(days=today.weekday())).strftime('%Y-%m-%d')
        end_week = today.strftime('%Y-%m-%d')
        self.load_stats_for_period(start_week, end_week, "Cette semaine")
    
    def load_month_stats(self, *args):
        """Charger les statistiques du mois"""
        today = datetime.now()
        start_month = today.replace(day=1).strftime('%Y-%m-%d')
        end_month = today.strftime('%Y-%m-%d')
        self.load_stats_for_period(start_month, end_month, "Ce mois")
    
    def load_year_stats(self, *args):
        """Charger les statistiques de l'année"""
        today = datetime.now()
        start_year = today.replace(month=1, day=1).strftime('%Y-%m-%d')
        end_year = today.strftime('%Y-%m-%d')
        self.load_stats_for_period(start_year, end_year, "Cette année")
    
    def load_stats_for_period(self, start_date, end_date, period_name):
        """Charger les statistiques pour une période donnée"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                db_manager = app.db_manager
                
                # Statistiques générales
                stats = {}
                
                # Nombre de ventes
                ventes_query = """
                    SELECT COUNT(*) as count, SUM(montant_ttc) as total
                    FROM ventes 
                    WHERE DATE(date_vente) BETWEEN ? AND ?
                """
                ventes_result = db_manager.execute_query(ventes_query, (start_date, end_date))
                if ventes_result:
                    stats['nb_ventes'] = ventes_result[0]['count'] or 0
                    stats['ca_total'] = ventes_result[0]['total'] or 0
                else:
                    stats['nb_ventes'] = 0
                    stats['ca_total'] = 0
                
                # Panier moyen
                stats['panier_moyen'] = stats['ca_total'] / stats['nb_ventes'] if stats['nb_ventes'] > 0 else 0
                
                # Nombre de clients actifs
                clients_query = """
                    SELECT COUNT(DISTINCT client_id) as count
                    FROM ventes 
                    WHERE DATE(date_vente) BETWEEN ? AND ?
                """
                clients_result = db_manager.execute_query(clients_query, (start_date, end_date))
                stats['clients_actifs'] = clients_result[0]['count'] if clients_result else 0
                
                # Évolution des ventes par jour
                evolution_query = """
                    SELECT DATE(date_vente) as date, COUNT(*) as nb_ventes, SUM(montant_ttc) as ca
                    FROM ventes 
                    WHERE DATE(date_vente) BETWEEN ? AND ?
                    GROUP BY DATE(date_vente)
                    ORDER BY DATE(date_vente)
                """
                stats['evolution'] = db_manager.execute_query(evolution_query, (start_date, end_date))
                
                # Top produits
                top_products_query = """
                    SELECT p.nom, SUM(dv.quantite) as quantite_vendue, SUM(dv.montant_ligne) as ca_produit
                    FROM details_vente dv
                    JOIN produits p ON dv.produit_id = p.id
                    JOIN ventes v ON dv.vente_id = v.id
                    WHERE DATE(v.date_vente) BETWEEN ? AND ?
                    GROUP BY p.id, p.nom
                    ORDER BY quantite_vendue DESC
                    LIMIT 10
                """
                stats['top_products'] = db_manager.execute_query(top_products_query, (start_date, end_date))
                
                # Top clients
                top_clients_query = """
                    SELECT COALESCE(c.prenom || ' ' || c.nom, c.entreprise, 'Client supprimé') as client_nom,
                           COUNT(v.id) as nb_achats, SUM(v.montant_ttc) as ca_client
                    FROM ventes v
                    LEFT JOIN clients c ON v.client_id = c.id
                    WHERE DATE(v.date_vente) BETWEEN ? AND ?
                    GROUP BY v.client_id, client_nom
                    ORDER BY ca_client DESC
                    LIMIT 10
                """
                stats['top_clients'] = db_manager.execute_query(top_clients_query, (start_date, end_date))
                
                # Statistiques des produits
                products_stats_query = """
                    SELECT 
                        COUNT(*) as total_produits,
                        SUM(CASE WHEN stock_actuel <= stock_minimum THEN 1 ELSE 0 END) as produits_stock_bas,
                        AVG(prix_vente) as prix_moyen
                    FROM produits 
                    WHERE actif = 1
                """
                products_stats = db_manager.execute_query(products_stats_query)
                if products_stats:
                    stats.update(products_stats[0])
                
                self.stats_data = stats
                self.period_name = period_name
                
                # Mettre à jour l'interface
                Clock.schedule_once(self.update_stats_ui, 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des statistiques: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_stats_ui(self, dt):
        """Mettre à jour l'interface des statistiques"""
        # Vue d'ensemble
        self.update_overview_stats()
        self.update_overview_chart()
        
        # Ventes
        self.update_sales_stats()
        self.update_sales_chart()
        
        # Produits
        self.update_products_stats()
        self.update_top_products()
        
        # Clients
        self.update_clients_stats()
        self.update_top_clients()
    
    def update_overview_stats(self):
        """Mettre à jour les statistiques de la vue d'ensemble"""
        self.overview_stats_grid.clear_widgets()
        
        stats = [
            ("Nombre de ventes", self.stats_data.get('nb_ventes', 0), self.period_name, "cart", (0.2, 0.6, 0.9, 1)),
            ("Chiffre d'affaires", f"{self.stats_data.get('ca_total', 0):.2f} DH", self.period_name, "currency-mad", (0.2, 0.7, 0.3, 1)),
            ("Panier moyen", f"{self.stats_data.get('panier_moyen', 0):.2f} DH", self.period_name, "calculator", (0.9, 0.5, 0.2, 1)),
            ("Clients actifs", self.stats_data.get('clients_actifs', 0), self.period_name, "account-group", (0.7, 0.2, 0.9, 1))
        ]
        
        for title, value, subtitle, icon, color in stats:
            card = StatisticCard(title, value, subtitle, icon, color)
            self.overview_stats_grid.add_widget(card)
    
    def update_overview_chart(self):
        """Mettre à jour le graphique de la vue d'ensemble"""
        self.overview_chart_container.clear_widgets()
        
        evolution_data = self.stats_data.get('evolution', [])
        if not evolution_data:
            no_data_label = MDLabel(
                text="Aucune donnée disponible pour cette période",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="100dp"
            )
            self.overview_chart_container.add_widget(no_data_label)
            return
        
        try:
            # Créer le graphique avec matplotlib
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6))
            fig.patch.set_facecolor('white')
            
            dates = [datetime.strptime(item['date'], '%Y-%m-%d') for item in evolution_data]
            nb_ventes = [item['nb_ventes'] for item in evolution_data]
            ca_values = [item['ca'] or 0 for item in evolution_data]
            
            # Graphique du nombre de ventes
            ax1.plot(dates, nb_ventes, marker='o', color='#2196F3', linewidth=2)
            ax1.set_title('Évolution du nombre de ventes')
            ax1.set_ylabel('Nombre de ventes')
            ax1.grid(True, alpha=0.3)
            
            # Graphique du chiffre d'affaires
            ax2.plot(dates, ca_values, marker='s', color='#4CAF50', linewidth=2)
            ax2.set_title('Évolution du chiffre d\'affaires')
            ax2.set_ylabel('CA (DH)')
            ax2.set_xlabel('Date')
            ax2.grid(True, alpha=0.3)
            
            # Formatage des dates
            for ax in [ax1, ax2]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(dates)//7)))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            # Ajouter le graphique à l'interface
            chart_widget = FigureCanvasKivyAgg(fig)
            chart_card = ChartCard(f"Évolution des ventes - {self.period_name}", chart_widget)
            self.overview_chart_container.add_widget(chart_card)
            
        except Exception as e:
            print(f"Erreur lors de la création du graphique: {e}")
            error_label = MDLabel(
                text="Erreur lors de la génération du graphique",
                theme_text_color="Error",
                halign="center",
                size_hint_y=None,
                height="100dp"
            )
            self.overview_chart_container.add_widget(error_label)
    
    def update_sales_stats(self):
        """Mettre à jour les statistiques des ventes"""
        self.sales_stats_grid.clear_widgets()
        
        # Mêmes stats que la vue d'ensemble mais avec plus de détails
        self.update_overview_stats()  # Réutiliser la même logique
    
    def update_sales_chart(self):
        """Mettre à jour les graphiques des ventes"""
        self.sales_chart_container.clear_widgets()
        
        # Réutiliser le graphique de la vue d'ensemble
        self.update_overview_chart()
    
    def update_products_stats(self):
        """Mettre à jour les statistiques des produits"""
        self.products_stats_grid.clear_widgets()
        
        stats = [
            ("Total produits", self.stats_data.get('total_produits', 0), "Produits actifs", "package-variant", (0.2, 0.6, 0.9, 1)),
            ("Stock bas", self.stats_data.get('produits_stock_bas', 0), "Produits à réapprovisionner", "alert-circle", (0.9, 0.4, 0.2, 1)),
            ("Prix moyen", f"{self.stats_data.get('prix_moyen', 0):.2f} DH", "Prix de vente moyen", "currency-mad", (0.2, 0.7, 0.3, 1)),
            ("Produits vendus", len(self.stats_data.get('top_products', [])), self.period_name, "cart-check", (0.7, 0.2, 0.9, 1))
        ]
        
        for title, value, subtitle, icon, color in stats:
            card = StatisticCard(title, value, subtitle, icon, color)
            self.products_stats_grid.add_widget(card)
    
    def update_top_products(self):
        """Mettre à jour le top des produits"""
        self.top_products_container.clear_widgets()
        
        top_products = self.stats_data.get('top_products', [])
        if not top_products:
            no_data_label = MDLabel(
                text="Aucun produit vendu sur cette période",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="50dp"
            )
            self.top_products_container.add_widget(no_data_label)
            return
        
        for i, product in enumerate(top_products[:10], 1):
            product_card = MDCard(
                elevation=1,
                padding="12dp",
                size_hint_y=None,
                height="60dp"
            )
            
            product_layout = MDBoxLayout(orientation='horizontal', spacing="8dp")
            
            # Rang
            rank_label = MDLabel(
                text=f"#{i}",
                font_style="H6",
                theme_text_color="Primary",
                size_hint_x=0.1
            )
            
            # Nom du produit
            name_label = MDLabel(
                text=product['nom'],
                font_style="Subtitle1",
                theme_text_color="Primary",
                size_hint_x=0.5
            )
            
            # Quantité vendue
            qty_label = MDLabel(
                text=f"{product['quantite_vendue']} vendus",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.2
            )
            
            # CA du produit
            ca_label = MDLabel(
                text=f"{product['ca_produit']:.2f} DH",
                font_style="Subtitle2",
                theme_text_color="Primary",
                size_hint_x=0.2
            )
            
            product_layout.add_widget(rank_label)
            product_layout.add_widget(name_label)
            product_layout.add_widget(qty_label)
            product_layout.add_widget(ca_label)
            
            product_card.add_widget(product_layout)
            self.top_products_container.add_widget(product_card)
    
    def update_clients_stats(self):
        """Mettre à jour les statistiques des clients"""
        self.clients_stats_grid.clear_widgets()
        
        # Calculer quelques stats supplémentaires
        top_clients = self.stats_data.get('top_clients', [])
        total_clients_actifs = len(top_clients)
        ca_moyen_client = sum(client['ca_client'] for client in top_clients) / total_clients_actifs if total_clients_actifs > 0 else 0
        
        stats = [
            ("Clients actifs", self.stats_data.get('clients_actifs', 0), self.period_name, "account-group", (0.2, 0.6, 0.9, 1)),
            ("CA moyen/client", f"{ca_moyen_client:.2f} DH", self.period_name, "calculator", (0.2, 0.7, 0.3, 1)),
            ("Meilleur client", f"{top_clients[0]['ca_client']:.2f} DH" if top_clients else "0 DH", "CA du meilleur client", "crown", (0.9, 0.7, 0.2, 1)),
            ("Clients fidèles", len([c for c in top_clients if c['nb_achats'] > 1]), "Plus d'un achat", "heart", (0.9, 0.2, 0.5, 1))
        ]
        
        for title, value, subtitle, icon, color in stats:
            card = StatisticCard(title, value, subtitle, icon, color)
            self.clients_stats_grid.add_widget(card)
    
    def update_top_clients(self):
        """Mettre à jour le top des clients"""
        self.top_clients_container.clear_widgets()
        
        top_clients = self.stats_data.get('top_clients', [])
        if not top_clients:
            no_data_label = MDLabel(
                text="Aucun client sur cette période",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="50dp"
            )
            self.top_clients_container.add_widget(no_data_label)
            return
        
        for i, client in enumerate(top_clients[:10], 1):
            client_card = MDCard(
                elevation=1,
                padding="12dp",
                size_hint_y=None,
                height="60dp"
            )
            
            client_layout = MDBoxLayout(orientation='horizontal', spacing="8dp")
            
            # Rang
            rank_label = MDLabel(
                text=f"#{i}",
                font_style="H6",
                theme_text_color="Primary",
                size_hint_x=0.1
            )
            
            # Nom du client
            name_label = MDLabel(
                text=client['client_nom'],
                font_style="Subtitle1",
                theme_text_color="Primary",
                size_hint_x=0.4
            )
            
            # Nombre d'achats
            achats_label = MDLabel(
                text=f"{client['nb_achats']} achat(s)",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.25
            )
            
            # CA du client
            ca_label = MDLabel(
                text=f"{client['ca_client']:.2f} DH",
                font_style="Subtitle2",
                theme_text_color="Primary",
                size_hint_x=0.25
            )
            
            client_layout.add_widget(rank_label)
            client_layout.add_widget(name_label)
            client_layout.add_widget(achats_label)
            client_layout.add_widget(ca_label)
            
            client_card.add_widget(client_layout)
            self.top_clients_container.add_widget(client_card)