"""
Test d'intégration du formulaire de catégories dans l'application principale
"""

import os
import sys
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

from kivy.app import App
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoriesScreen
from database.db_manager import DatabaseManager


class TestCategoriesIntegrationApp(MDApp):
    """Application de test pour l'intégration des catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Intégration Catégories - GesComPro"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if self.db_manager.connect():
            self.db_manager.initialize_database()
            print("✅ Base de données initialisée")
        else:
            print("❌ Erreur d'initialisation de la base de données")
    
    def build(self):
        """Construire l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="40dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test Intégration Écran Catégories",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_y=None,
            height="60dp",
            halign="center"
        )
        
        # Description
        description = MDLabel(
            text="Test de l'écran des catégories avec le formulaire corrigé.",
            font_style="Body1",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        # Bouton pour ouvrir l'écran des catégories
        categories_btn = MDRaisedButton(
            text="📂 Ouvrir Écran Catégories",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.open_categories_screen
        )
        
        # Statut
        self.status_label = MDLabel(
            text="✅ Prêt pour le test",
            font_style="Body2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(description)
        layout.add_widget(categories_btn)
        layout.add_widget(self.status_label)
        
        screen.add_widget(layout)
        return screen
    
    def open_categories_screen(self, *args):
        """Ouvrir l'écran des catégories"""
        try:
            self.status_label.text = "🔄 Chargement de l'écran des catégories..."
            
            # Créer l'écran des catégories
            categories_screen = CategoriesScreen(name='categories_test')
            
            # Remplacer l'écran actuel
            self.root.clear_widgets()
            self.root.add_widget(categories_screen)
            
            self.status_label.text = "✅ Écran des catégories chargé"
            
        except Exception as e:
            self.status_label.text = f"❌ Erreur: {str(e)}"
            print(f"Erreur lors de l'ouverture de l'écran: {e}")
            import traceback
            traceback.print_exc()
    
    def on_stop(self):
        """Actions à effectuer à la fermeture"""
        if hasattr(self, 'db_manager'):
            self.db_manager.close()


if __name__ == '__main__':
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    # Lancement de l'application de test
    TestCategoriesIntegrationApp().run()
