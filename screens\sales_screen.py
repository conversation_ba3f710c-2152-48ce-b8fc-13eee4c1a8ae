"""
Écran de gestion des ventes
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.app import MDApp
from kivy.clock import Clock
from datetime import datetime
import threading


class SaleCard(MDCard):
    """Carte pour afficher une vente"""
    
    def __init__(self, sale_data, on_view_callback, on_edit_callback, **kwargs):
        super().__init__(**kwargs)
        self.sale_data = sale_data
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "140dp"
        self.spacing = "8dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="4dp")
        
        # En-tête avec numéro de facture et actions
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        facture_label = MDLabel(
            text=f"Facture: {sale_data.get('numero_facture', 'N/A')}",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.6
        )
        
        # Statut avec icône
        statut = sale_data.get('statut', 'En cours')
        statut_info = {
            'En cours': {'icon': '🔄', 'color': [1, 0.6, 0, 1], 'text': 'En cours'},
            'Payée': {'icon': '✅', 'color': [0, 0.8, 0, 1], 'text': 'Payée'},
            'Annulée': {'icon': '❌', 'color': [0.8, 0, 0, 1], 'text': 'Annulée'}
        }.get(statut, {'icon': '❓', 'color': [0.5, 0.5, 0.5, 1], 'text': statut})
        
        statut_label = MDLabel(
            text=f"{statut_info['icon']} {statut_info['text']}",
            font_style="Caption",
            theme_text_color="Custom",
            text_color=statut_info['color'],
            size_hint_x=0.2,
            bold=True
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.2, spacing="4dp")
        
        view_btn = MDIconButton(
            icon="eye",
            theme_icon_color="Primary",
            on_release=lambda x: on_view_callback(sale_data)
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Secondary",
            on_release=lambda x: on_edit_callback(sale_data)
        )
        
        actions_layout.add_widget(view_btn)
        actions_layout.add_widget(edit_btn)
        
        header_layout.add_widget(facture_label)
        header_layout.add_widget(statut_label)
        header_layout.add_widget(actions_layout)
        
        # Informations de la vente
        info_layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # Client et date
        client_date_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="20dp")
        
        client_nom = sale_data.get('client_nom', 'Client non spécifié')
        client_label = MDLabel(
            text=f"Client: {client_nom}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.6
        )
        
        date_vente = sale_data.get('date_vente', '')
        if date_vente and isinstance(date_vente, str):
            try:
                date_obj = datetime.fromisoformat(date_vente.replace('Z', '+00:00'))
                date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
            except:
                date_formatted = str(date_vente)
        else:
            date_formatted = 'Date inconnue'
        
        date_label = MDLabel(
            text=date_formatted,
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        
        client_date_layout.add_widget(client_label)
        client_date_layout.add_widget(date_label)
        
        # Montants
        montants_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="20dp")
        
        montant_ht = sale_data.get('montant_ht', 0)
        montant_ttc = sale_data.get('montant_ttc', 0)
        
        montant_ht_label = MDLabel(
            text=f"HT: {montant_ht:.2f} DH",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        montant_ttc_label = MDLabel(
            text=f"TTC: {montant_ttc:.2f} DH",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_x=0.5
        )
        
        montants_layout.add_widget(montant_ht_label)
        montants_layout.add_widget(montant_ttc_label)
        
        info_layout.add_widget(client_date_layout)
        info_layout.add_widget(montants_layout)
        
        # Mode de paiement
        if sale_data.get('mode_paiement'):
            paiement_label = MDLabel(
                text=f"Paiement: {sale_data['mode_paiement']}",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(paiement_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(info_layout)
        self.add_widget(layout)


class NewSaleDialog(MDDialog):
    """Dialog pour créer une nouvelle vente"""
    
    def __init__(self, clients=None, products=None, on_save_callback=None, **kwargs):
        self.clients = clients or []
        self.products = products or []
        self.on_save_callback = on_save_callback
        self.selected_client = None
        self.sale_items = []  # Liste des produits dans la vente
        
        # Création du formulaire
        form_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Sélection du client
        client_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp")
        client_label = MDLabel(text="Client:", size_hint_x=0.3)
        
        self.client_field = MDTextField(
            hint_text="Sélectionner un client",
            readonly=True,
            size_hint_x=0.7
        )
        
        # Menu déroulant pour les clients
        client_menu_items = []
        for client in self.clients:
            nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
            if not nom_complet:
                nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
            
            client_menu_items.append({
                "text": nom_complet,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=client: self.select_client(x)
            })
        
        self.client_menu = MDDropdownMenu(
            caller=self.client_field,
            items=client_menu_items,
            max_height="200dp"
        )
        
        self.client_field.bind(on_release=self.client_menu.open)
        
        client_layout.add_widget(client_label)
        client_layout.add_widget(self.client_field)
        
        # Mode de paiement
        paiement_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp")
        paiement_label = MDLabel(text="Paiement:", size_hint_x=0.3)
        
        # Liste déroulante pour les modes de paiement
        self.paiement_field = MDTextField(
            hint_text="Sélectionner le mode de paiement",
            text="Espèces",
            size_hint_x=0.7,
            readonly=True
        )
        
        # Créer le menu déroulant pour les modes de paiement
        paiement_modes = [
            "Espèces",
            "Carte bancaire",
            "Chèque",
            "Virement bancaire",
            "Électronique",
            "Crédit",
            "Autre"
        ]
        
        paiement_menu_items = []
        for mode in paiement_modes:
            paiement_menu_items.append({
                "text": mode,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=mode: self.select_paiement_mode(x)
            })
        
        self.paiement_menu = MDDropdownMenu(
            caller=self.paiement_field,
            items=paiement_menu_items,
            width_mult=4,
            max_height="200dp"
        )
        
        self.paiement_field.bind(on_release=self.paiement_menu.open)
        
        paiement_layout.add_widget(paiement_label)
        paiement_layout.add_widget(self.paiement_field)
        
        # Notes
        self.notes_field = MDTextField(
            hint_text="Notes (optionnel)",
            multiline=True,
            max_height="80dp"
        )
        
        # Section des produits
        products_label = MDLabel(
            text="Produits:",
            font_style="Subtitle1",
            size_hint_y=None,
            height="32dp"
        )
        
        # Liste des produits sélectionnés
        self.products_scroll = MDScrollView(size_hint_y=None, height="200dp")
        self.products_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        self.products_scroll.add_widget(self.products_layout)
        
        # Bouton pour ajouter un produit
        add_product_btn = MDRaisedButton(
            text="Ajouter un produit",
            icon="plus",
            on_release=self.add_product_dialog,
            size_hint_y=None,
            height="40dp"
        )
        
        # Totaux
        self.totaux_label = MDLabel(
            text="Total HT: 0.00 DH | Total TTC: 0.00 DH",
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Ajout des éléments au formulaire
        form_layout.add_widget(client_layout)
        form_layout.add_widget(paiement_layout)
        form_layout.add_widget(self.notes_field)
        form_layout.add_widget(products_label)
        form_layout.add_widget(self.products_scroll)
        form_layout.add_widget(add_product_btn)
        form_layout.add_widget(self.totaux_label)
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="CRÉER LA VENTE",
                on_release=self.save_sale
            )
        ]
        
        super().__init__(
            title="Nouvelle vente",
            type="custom",
            content_cls=form_layout,
            buttons=buttons,
            size_hint=(0.95, None),
            height="700dp",
            **kwargs
        )
    
    def select_client(self, client):
        """Sélectionner un client"""
        self.selected_client = client
        nom_complet = f"{client.get('prenom', '')} {client.get('nom', '')}".strip()
        if not nom_complet:
            nom_complet = client.get('entreprise', f"Client {client.get('id', '')}")
        self.client_field.text = nom_complet
        self.client_menu.dismiss()
    
    def select_paiement_mode(self, mode):
        """Sélectionner un mode de paiement"""
        self.paiement_field.text = mode
        self.paiement_menu.dismiss()
    
    def add_product_dialog(self, *args):
        """Ouvrir le dialog pour ajouter un produit"""
        # Créer une liste des produits disponibles
        product_items = []
        for product in self.products:
            if product.get('stock_actuel', 0) > 0:  # Seulement les produits en stock
                product_items.append({
                    "text": f"{product.get('nom', '')} - {product.get('prix_vente', 0):.2f} DH (Stock: {product.get('stock_actuel', 0)})",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=product: self.add_product_to_sale(x)
                })
        
        if not product_items:
            # Aucun produit en stock
            no_stock_dialog = MDDialog(
                title="Aucun produit disponible",
                text="Aucun produit n'est actuellement en stock.",
                buttons=[
                    MDFlatButton(
                        text="OK",
                        on_release=lambda x: no_stock_dialog.dismiss()
                    )
                ]
            )
            no_stock_dialog.open()
            return
        
        # Menu de sélection des produits
        product_menu = MDDropdownMenu(
            caller=self.products_scroll,
            items=product_items,
            max_height="300dp"
        )
        product_menu.open()
    
    def add_product_to_sale(self, product):
        """Ajouter un produit à la vente"""
        # Vérifier si le produit est déjà dans la liste
        for item in self.sale_items:
            if item['product']['id'] == product['id']:
                # Augmenter la quantité
                if item['quantite'] < product.get('stock_actuel', 0):
                    item['quantite'] += 1
                    self.update_products_display()
                    self.calculate_totals()
                return
        
        # Ajouter le nouveau produit
        self.sale_items.append({
            'product': product,
            'quantite': 1,
            'prix_unitaire': product.get('prix_vente', 0)
        })
        
        self.update_products_display()
        self.calculate_totals()
    
    def update_products_display(self):
        """Mettre à jour l'affichage des produits"""
        self.products_layout.clear_widgets()
        
        for i, item in enumerate(self.sale_items):
            product = item['product']
            quantite = item['quantite']
            prix_unitaire = item['prix_unitaire']
            total_ligne = quantite * prix_unitaire
            
            # Carte pour chaque produit
            product_card = MDCard(
                elevation=1,
                padding="8dp",
                size_hint_y=None,
                height="80dp"
            )
            
            product_layout = MDBoxLayout(orientation='horizontal', spacing="8dp")
            
            # Informations du produit
            info_layout = MDBoxLayout(orientation='vertical', size_hint_x=0.6)
            
            nom_label = MDLabel(
                text=product.get('nom', ''),
                font_style="Subtitle2",
                theme_text_color="Primary"
            )
            
            prix_label = MDLabel(
                text=f"{quantite} x {prix_unitaire:.2f} DH = {total_ligne:.2f} DH",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            
            info_layout.add_widget(nom_label)
            info_layout.add_widget(prix_label)
            
            # Contrôles de quantité
            qty_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.3, spacing="4dp")
            
            minus_btn = MDIconButton(
                icon="minus",
                theme_icon_color="Primary",
                on_release=lambda x, idx=i: self.decrease_quantity(idx)
            )
            
            qty_label = MDLabel(
                text=str(quantite),
                halign="center",
                font_style="Subtitle1"
            )
            
            plus_btn = MDIconButton(
                icon="plus",
                theme_icon_color="Primary",
                on_release=lambda x, idx=i: self.increase_quantity(idx)
            )
            
            qty_layout.add_widget(minus_btn)
            qty_layout.add_widget(qty_label)
            qty_layout.add_widget(plus_btn)
            
            # Bouton de suppression
            delete_btn = MDIconButton(
                icon="delete",
                theme_icon_color="Error",
                size_hint_x=0.1,
                on_release=lambda x, idx=i: self.remove_product(idx)
            )
            
            product_layout.add_widget(info_layout)
            product_layout.add_widget(qty_layout)
            product_layout.add_widget(delete_btn)
            
            product_card.add_widget(product_layout)
            self.products_layout.add_widget(product_card)
    
    def increase_quantity(self, index):
        """Augmenter la quantité d'un produit"""
        if index < len(self.sale_items):
            item = self.sale_items[index]
            max_stock = item['product'].get('stock_actuel', 0)
            if item['quantite'] < max_stock:
                item['quantite'] += 1
                self.update_products_display()
                self.calculate_totals()
    
    def decrease_quantity(self, index):
        """Diminuer la quantité d'un produit"""
        if index < len(self.sale_items):
            item = self.sale_items[index]
            if item['quantite'] > 1:
                item['quantite'] -= 1
                self.update_products_display()
                self.calculate_totals()
    
    def remove_product(self, index):
        """Supprimer un produit de la vente"""
        if index < len(self.sale_items):
            self.sale_items.pop(index)
            self.update_products_display()
            self.calculate_totals()
    
    def calculate_totals(self):
        """Calculer les totaux"""
        total_ht = 0
        total_tva = 0
        
        for item in self.sale_items:
            quantite = item['quantite']
            prix_unitaire = item['prix_unitaire']
            tva_rate = item['product'].get('tva', 20.0) / 100
            
            montant_ligne_ht = quantite * prix_unitaire
            montant_tva_ligne = montant_ligne_ht * tva_rate
            
            total_ht += montant_ligne_ht
            total_tva += montant_tva_ligne
        
        total_ttc = total_ht + total_tva
        
        self.totaux_label.text = f"Total HT: {total_ht:.2f} DH | TVA: {total_tva:.2f} DH | Total TTC: {total_ttc:.2f} DH"
    
    def save_sale(self, *args):
        """Enregistrer la vente"""
        # Validation
        if not self.selected_client:
            return  # Afficher une erreur
        
        if not self.sale_items:
            return  # Afficher une erreur
        
        # Préparer les données de la vente
        sale_data = {
            'client': self.selected_client,
            'items': self.sale_items,
            'mode_paiement': self.paiement_field.text.strip(),
            'notes': self.notes_field.text.strip()
        }
        
        # Appeler le callback
        if self.on_save_callback:
            self.on_save_callback(sale_data)
        
        self.dismiss()


class SalesScreen(MDScreen):
    """Écran de gestion des ventes"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sales_data = []
        self.filtered_sales = []
        self.clients = []
        self.products = []
        self.build_ui()
    
    def build_ui(self):
        """Construction de l'interface utilisateur"""
        main_layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête avec titre et bouton d'ajout
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="48dp")
        
        title_label = MDLabel(
            text="Gestion des Ventes",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        add_button = MDRaisedButton(
            text="Nouvelle Vente",
            icon="plus",
            on_release=self.add_sale,
            size_hint_x=0.3
        )
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(add_button)
        
        # Barre de recherche et filtres
        search_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp", spacing="8dp")
        
        self.search_field = MDTextField(
            hint_text="Rechercher une vente...",
            icon_left="magnify",
            size_hint_x=0.7,
            on_text=self.filter_sales
        )
        
        today_button = MDRaisedButton(
            text="Aujourd'hui",
            icon="calendar-today",
            size_hint_x=0.3,
            on_release=self.show_today_sales
        )
        
        search_layout.add_widget(self.search_field)
        search_layout.add_widget(today_button)
        
        # ScrollView pour la liste des ventes
        self.scroll = MDScrollView()
        self.sales_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
        self.scroll.add_widget(self.sales_layout)
        
        # Message quand aucune vente
        self.no_sales_label = MDLabel(
            text="Aucune vente trouvée",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        main_layout.add_widget(header_layout)
        main_layout.add_widget(search_layout)
        main_layout.add_widget(self.scroll)
        
        self.add_widget(main_layout)
    
    def on_enter(self):
        """Actions à effectuer lors de l'entrée sur l'écran"""
        self.load_sales()
        self.load_clients_and_products()
    
    def load_sales(self):
        """Charger la liste des ventes"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                if hasattr(app, 'db_manager'):
                    # Import des fonctions de base de données
                    from database.db_manager import get_all_sales
                    self.sales_data = get_all_sales(app.db_manager)
                    
                    # Mettre à jour l'interface
                    Clock.schedule_once(self.update_sales_ui, 0)
                else:
                    print("❌ Gestionnaire de base de données non disponible")
                    self.sales_data = []
                    Clock.schedule_once(self.update_sales_ui, 0)
                
            except Exception as e:
                print(f"Erreur lors du chargement des ventes: {e}")
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def load_clients_and_products(self):
        """Charger les clients et produits"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                if hasattr(app, 'db_manager'):
                    # Import des fonctions de base de données
                    from database.db_manager import get_all_clients, get_all_products
                    
                    self.clients = get_all_clients(app.db_manager)
                    self.products = get_all_products(app.db_manager)
                else:
                    print("❌ Gestionnaire de base de données non disponible")
                    self.clients = []
                    self.products = []
                
            except Exception as e:
                print(f"Erreur lors du chargement des clients/produits: {e}")
                self.clients = []
                self.products = []
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_sales_ui(self, dt):
        """Mettre à jour l'interface des ventes"""
        self.sales_layout.clear_widgets()
        
        sales_to_show = self.filtered_sales if hasattr(self, 'filtered_sales') and self.filtered_sales else self.sales_data
        
        if not sales_to_show:
            self.sales_layout.add_widget(self.no_sales_label)
        else:
            for sale in sales_to_show:
                sale_card = SaleCard(
                    sale,
                    self.view_sale,
                    self.edit_sale
                )
                self.sales_layout.add_widget(sale_card)
    
    def filter_sales(self, instance, text):
        """Filtrer les ventes selon le texte de recherche"""
        if not text.strip():
            self.filtered_sales = self.sales_data
        else:
            search_text = text.lower()
            self.filtered_sales = []
            
            for sale in self.sales_data:
                # Recherche dans numéro de facture, nom du client
                searchable_fields = [
                    str(sale.get('numero_facture', '')),
                    str(sale.get('client_nom', '')),
                    str(sale.get('notes', ''))
                ]
                
                if any(search_text in field.lower() for field in searchable_fields if field):
                    self.filtered_sales.append(sale)
        
        self.update_sales_ui(None)
    
    def show_today_sales(self, *args):
        """Afficher les ventes du jour"""
        today = datetime.now().strftime('%Y-%m-%d')
        self.filtered_sales = [
            sale for sale in self.sales_data
            if sale.get('date_vente', '').startswith(today)
        ]
        self.update_sales_ui(None)
    
    def add_sale(self, *args):
        """Ajouter une nouvelle vente"""
        dialog = NewSaleDialog(
            clients=self.clients,
            products=self.products,
            on_save_callback=self.save_sale
        )
        dialog.open()
    
    def save_sale(self, sale_data):
        """Enregistrer une nouvelle vente"""
        def save_data():
            try:
                app = MDApp.get_running_app()
                if hasattr(app, 'db_manager'):
                    # Import de la fonction de création de vente
                    from database.db_manager import create_sale
                    
                    # Préparer les données de la vente
                    vente_data = {
                        'client_id': sale_data.get('client', {}).get('id'),
                        'mode_paiement': sale_data.get('mode_paiement', 'Espèces'),
                        'notes': sale_data.get('notes', ''),
                        'statut': 'En cours'
                    }
                    
                    # Préparer les articles de la vente
                    sale_items = sale_data.get('items', [])
                    
                    # Créer la vente
                    vente_id = create_sale(app.db_manager, vente_data, sale_items)
                    
                    if vente_id:
                        # Recharger la liste des ventes
                        Clock.schedule_once(lambda dt: self.load_sales(), 0)
                        print(f"✅ Vente créée avec succès (ID: {vente_id})")
                    else:
                        print("❌ Erreur lors de la création de la vente")
                else:
                    print("❌ Gestionnaire de base de données non disponible")
                
            except Exception as e:
                print(f"❌ Erreur lors de l'enregistrement de la vente: {e}")
                import traceback
                traceback.print_exc()
        
        threading.Thread(target=save_data, daemon=True).start()
    
    def view_sale(self, sale_data):
        """Voir les détails d'une vente"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                from database.db_manager import get_sale_by_id
                
                # Récupérer les détails complets de la vente
                sale_details = get_sale_by_id(app.db_manager, sale_data.get('id'))
                
                if sale_details:
                    # Créer un dialog pour afficher les détails
                    self.show_sale_details_dialog(sale_details)
                else:
                    print(f"❌ Vente non trouvée: {sale_data.get('numero_facture')}")
            else:
                print("❌ Gestionnaire de base de données non disponible")
                
        except Exception as e:
            print(f"❌ Erreur lors de la récupération de la vente: {e}")
    
    def edit_sale(self, sale_data):
        """Modifier une vente - Afficher un dialog avec les options"""
        try:
            current_status = sale_data.get('statut', 'En cours')
            
            # Créer le contenu du dialog
            content_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
            
            # Informations de la vente
            info_text = f"""
Facture: {sale_data.get('numero_facture', 'N/A')}
Client: {sale_data.get('client_nom', 'N/A')}
Montant: {sale_data.get('montant_ttc', 0):.2f} DH
Statut actuel: {current_status}
            """.strip()
            
            info_label = MDLabel(
                text=info_text,
                theme_text_color="Primary",
                size_hint_y=None,
                height="100dp"
            )
            content_layout.add_widget(info_label)
            
            # Boutons d'action selon le statut actuel
            buttons_layout = MDBoxLayout(orientation='vertical', spacing="8dp", adaptive_height=True)
            
            if current_status != 'Payée':
                paye_btn = MDRaisedButton(
                    text="✅ Marquer comme PAYÉE",
                    theme_icon_color="Custom",
                    icon_color="green",
                    size_hint_y=None,
                    height="48dp",
                    on_release=lambda x: self.confirm_status_change(sale_data, 'Payée')
                )
                buttons_layout.add_widget(paye_btn)
            
            if current_status != 'Annulée':
                annule_btn = MDRaisedButton(
                    text="❌ Marquer comme ANNULÉE",
                    theme_icon_color="Custom",
                    icon_color="red",
                    size_hint_y=None,
                    height="48dp",
                    on_release=lambda x: self.confirm_status_change(sale_data, 'Annulée')
                )
                buttons_layout.add_widget(annule_btn)
            
            if current_status != 'En cours':
                encours_btn = MDRaisedButton(
                    text="🔄 Marquer comme EN COURS",
                    theme_icon_color="Custom",
                    icon_color="orange",
                    size_hint_y=None,
                    height="48dp",
                    on_release=lambda x: self.confirm_status_change(sale_data, 'En cours')
                )
                buttons_layout.add_widget(encours_btn)
            
            content_layout.add_widget(buttons_layout)
            
            # Créer le dialog
            self.edit_dialog = MDDialog(
                title=f"Modifier la vente {sale_data.get('numero_facture')}",
                type="custom",
                content_cls=content_layout,
                buttons=[
                    MDFlatButton(
                        text="FERMER",
                        on_release=lambda x: self.edit_dialog.dismiss()
                    )
                ],
                size_hint=(0.8, None),
                height="400dp"
            )
            
            self.edit_dialog.open()
                
        except Exception as e:
            print(f"❌ Erreur lors de la modification de la vente: {e}")
    
    def confirm_status_change(self, sale_data, new_status):
        """Confirmer le changement de statut"""
        try:
            # Fermer le dialog d'édition
            if hasattr(self, 'edit_dialog'):
                self.edit_dialog.dismiss()
            
            # Messages de confirmation selon le statut
            if new_status == 'Payée':
                message = f"Confirmer que la vente {sale_data.get('numero_facture')} est PAYÉE ?"
                icon = "✅"
            elif new_status == 'Annulée':
                message = f"Confirmer l'ANNULATION de la vente {sale_data.get('numero_facture')} ?\n\n⚠️ Le stock sera restauré."
                icon = "❌"
            else:
                message = f"Remettre la vente {sale_data.get('numero_facture')} EN COURS ?"
                icon = "🔄"
            
            # Dialog de confirmation
            self.confirm_dialog = MDDialog(
                title=f"{icon} Confirmation",
                text=message,
                buttons=[
                    MDFlatButton(
                        text="ANNULER",
                        on_release=lambda x: self.confirm_dialog.dismiss()
                    ),
                    MDRaisedButton(
                        text="CONFIRMER",
                        on_release=lambda x: self.execute_status_change(sale_data, new_status)
                    )
                ]
            )
            
            self.confirm_dialog.open()
            
        except Exception as e:
            print(f"❌ Erreur confirmation: {e}")
    
    def execute_status_change(self, sale_data, new_status):
        """Exécuter le changement de statut"""
        try:
            # Fermer le dialog de confirmation
            if hasattr(self, 'confirm_dialog'):
                self.confirm_dialog.dismiss()
            
            # Mettre à jour le statut
            self.update_sale_status(sale_data, new_status)
            
        except Exception as e:
            print(f"❌ Erreur exécution changement: {e}")
    
    def update_sale_status(self, sale_data, new_status):
        """Mettre à jour le statut d'une vente"""
        def update_status():
            try:
                app = MDApp.get_running_app()
                if not hasattr(app, 'db_manager') or not app.db_manager:
                    print("❌ Gestionnaire de base de données non disponible")
                    return
                
                # S'assurer que la connexion est active
                if not app.db_manager.connection:
                    if not app.db_manager.connect():
                        print("❌ Impossible de se connecter à la base de données")
                        return
                
                from database.db_manager import update_sale_status, get_sale_by_id
                
                # Si on annule la vente, restaurer le stock (sauf si déjà annulée)
                if new_status == 'Annulée' and sale_data.get('statut') != 'Annulée':
                    try:
                        # Récupérer les détails de la vente pour restaurer le stock
                        sale_details = get_sale_by_id(app.db_manager, sale_data.get('id'))
                        
                        if sale_details and sale_details.get('details'):
                            # S'assurer que la connexion est toujours active
                            if not app.db_manager.connection:
                                app.db_manager.connect()
                            
                            cursor = app.db_manager.connection.cursor()
                            
                            for detail in sale_details['details']:
                                # Restaurer le stock
                                restore_query = """
                                    UPDATE produits 
                                    SET stock_actuel = stock_actuel + ? 
                                    WHERE id = ?
                                """
                                cursor.execute(restore_query, (
                                    detail.get('quantite', 0), 
                                    detail.get('produit_id')
                                ))
                            
                            app.db_manager.connection.commit()
                            print(f"✅ Stock restauré pour la vente annulée")
                    except Exception as e:
                        print(f"⚠️ Erreur lors de la restauration du stock: {e}")
                        # Continuer quand même avec la mise à jour du statut
                
                # Mettre à jour le statut
                success = update_sale_status(app.db_manager, sale_data.get('id'), new_status)
                
                if success:
                    # Messages selon le statut
                    if new_status == 'Payée':
                        message = f"✅ Vente {sale_data.get('numero_facture')} marquée comme PAYÉE"
                    elif new_status == 'Annulée':
                        message = f"❌ Vente {sale_data.get('numero_facture')} ANNULÉE - Stock restauré"
                    else:
                        message = f"🔄 Vente {sale_data.get('numero_facture')} remise EN COURS"
                    
                    print(message)
                    
                    # Recharger la liste des ventes
                    Clock.schedule_once(lambda dt: self.load_sales(), 0.1)
                else:
                    print(f"❌ Erreur lors de la mise à jour du statut")
                    
            except Exception as e:
                print(f"❌ Erreur lors de la mise à jour du statut: {e}")
                import traceback
                traceback.print_exc()
        
        # Exécuter la mise à jour
        update_status()
    
    def show_sale_details_dialog(self, sale_details):
        """Afficher les détails d'une vente dans un dialog"""
        from utils.helpers import format_currency
        
        # Créer le contenu du dialog
        content_layout = MDBoxLayout(orientation='vertical', spacing="16dp", adaptive_height=True)
        
        # Informations générales
        info_text = f"""
Facture: {sale_details.get('numero_facture', 'N/A')}
Date: {sale_details.get('date_vente', 'N/A')}
Client: {sale_details.get('client_nom', '')} {sale_details.get('client_prenom', '')}
Statut: {sale_details.get('statut', 'N/A')}
Mode de paiement: {sale_details.get('mode_paiement', 'N/A')}

Montant HT: {format_currency(sale_details.get('montant_ht', 0))}
Montant TVA: {format_currency(sale_details.get('montant_tva', 0))}
Montant TTC: {format_currency(sale_details.get('montant_ttc', 0))}
        """.strip()
        
        info_label = MDLabel(
            text=info_text,
            theme_text_color="Primary",
            size_hint_y=None,
            height="200dp"
        )
        
        content_layout.add_widget(info_label)
        
        # Détails des produits
        if sale_details.get('details'):
            products_label = MDLabel(
                text="Produits:",
                font_style="Subtitle1",
                theme_text_color="Primary",
                size_hint_y=None,
                height="32dp"
            )
            content_layout.add_widget(products_label)
            
            for detail in sale_details['details']:
                product_text = f"• {detail.get('produit_nom', 'N/A')} - Qté: {detail.get('quantite', 0)} - Prix: {format_currency(detail.get('prix_unitaire', 0))} - Total: {format_currency(detail.get('montant_ligne', 0))}"
                
                product_label = MDLabel(
                    text=product_text,
                    theme_text_color="Secondary",
                    size_hint_y=None,
                    height="24dp"
                )
                content_layout.add_widget(product_label)
        
        # Notes
        if sale_details.get('notes'):
            notes_label = MDLabel(
                text=f"Notes: {sale_details.get('notes')}",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="32dp"
            )
            content_layout.add_widget(notes_label)
        
        # Créer le dialog
        dialog = MDDialog(
            title=f"Détails de la vente {sale_details.get('numero_facture')}",
            type="custom",
            content_cls=content_layout,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: dialog.dismiss()
                )
            ],
            size_hint=(0.9, None),
            height="600dp"
        )
        
        dialog.open()