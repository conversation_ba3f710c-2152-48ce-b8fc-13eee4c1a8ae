"""
Écran de gestion des catégories - Version nettoyée
Utilise le nouveau formulaire basé sur la structure de la table categories
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MD<PERSON>crollView
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton, MDFlatButton, MDRaisedButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.dialog import MDDialog
from kivymd.app import MDApp
from kivy.clock import Clock
from kivy.metrics import dp
import threading
from database.db_manager import DatabaseManager
from new_category_form import CategoryFormDialog


class CategoryCard(MDCard):
    """Carte d'affichage d'une catégorie"""
    
    def __init__(self, category_data, on_edit_callback=None, on_delete_callback=None, **kwargs):
        super().__init__(**kwargs)
        self.category_data = category_data
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        
        # Style de la carte
        self.elevation = 2
        self.radius = [8]
        self.size_hint_y = None
        self.height = "120dp"
        self.padding = "16dp"
        self.spacing = "8dp"
        
        self.create_content()
    
    def create_content(self):
        """Créer le contenu de la carte"""
        layout = MDBoxLayout(orientation='vertical')
        
        # En-tête avec nom et boutons
        header_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="40dp"
        )
        
        # Nom de la catégorie
        name_label = MDLabel(
            text=f"📂 {self.category_data.get('nom', 'Sans nom')}",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_x=0.3,
            spacing="8dp"
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Custom",
            icon_color=[0.2, 0.6, 1, 1],
            on_release=self.edit_category
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Custom",
            icon_color=[1, 0.3, 0.3, 1],
            on_release=self.delete_category
        )
        
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(delete_btn)
        
        header_layout.add_widget(name_label)
        header_layout.add_widget(buttons_layout)
        
        # Description
        description = self.category_data.get('description', 'Aucune description')
        if len(description) > 80:
            description = description[:80] + "..."
        
        description_label = MDLabel(
            text=description,
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="32dp"
        )
        
        # Statistiques
        stats_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="24dp"
        )
        
        products_count = self.category_data.get('products_count', 0)
        products_label = MDLabel(
            text=f"📦 {products_count} produit(s)",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        date_creation = self.category_data.get('date_creation', '')
        if date_creation:
            try:
                from datetime import datetime
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y')
                else:
                    date_formatted = str(date_creation)[:10]
            except:
                date_formatted = str(date_creation)[:10]
        else:
            date_formatted = "Non définie"
        
        date_label = MDLabel(
            text=f"📅 {date_formatted}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        stats_layout.add_widget(products_label)
        stats_layout.add_widget(date_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(description_label)
        layout.add_widget(stats_layout)
        
        self.add_widget(layout)
    
    def edit_category(self, *args):
        """Modifier la catégorie"""
        if self.on_edit_callback:
            self.on_edit_callback(self.category_data)
    
    def delete_category(self, *args):
        """Supprimer la catégorie"""
        if self.on_delete_callback:
            self.on_delete_callback(self.category_data)


# ============================================================================
# FORMULAIRE DE CATÉGORIE
# ============================================================================
# L'ancienne classe CategoryFormDialog a été supprimée et remplacée par
# une nouvelle version dans new_category_form.py basée sur la structure
# exacte de la table categories de la base de données.
#
# Nouvelle structure basée sur la table :
# - id INTEGER PRIMARY KEY AUTOINCREMENT
# - nom TEXT NOT NULL UNIQUE  
# - description TEXT
# - date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
#
# Import : from new_category_form import CategoryFormDialog
# ============================================================================


class CategoriesScreen(MDScreen):
    """Écran optimisé de gestion des catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db_manager = DatabaseManager()
        self.categories_data = []
        self.create_interface()
        self.load_categories()
    
    def create_interface(self):
        """Créer l'interface optimisée"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📂 Gestion des Catégories",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[
                ["plus", lambda x: self.add_category()],
                ["refresh", lambda x: self.refresh_categories()]
            ],
            elevation=2
        )
        
        # Contenu principal
        content_layout = MDBoxLayout(
            orientation='vertical',
            padding="16dp",
            spacing="16dp"
        )
        
        # Zone de défilement pour les catégories
        self.scroll_view = MDScrollView()
        self.categories_container = MDBoxLayout(
            orientation='vertical',
            spacing="12dp",
            size_hint_y=None,
            height="0dp"
        )
        self.categories_container.bind(minimum_height=self.categories_container.setter('height'))
        
        self.scroll_view.add_widget(self.categories_container)
        content_layout.add_widget(self.scroll_view)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(content_layout)
        
        self.add_widget(main_layout)
    
    def load_categories(self):
        """Charger les catégories depuis la base de données"""
        def load_in_background():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Impossible de se connecter à la base de données"))
                    return
                
                # Requête basée sur la structure de la table categories
                categories = self.db_manager.execute_query("""
                    SELECT 
                        c.id,
                        c.nom,
                        c.description,
                        c.date_creation,
                        COUNT(p.id) as products_count
                    FROM categories c
                    LEFT JOIN produits p ON c.id = p.categorie_id
                    GROUP BY c.id, c.nom, c.description, c.date_creation
                    ORDER BY c.nom
                """)
                
                self.categories_data = categories or []
                Clock.schedule_once(lambda dt: self.update_categories_display())
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur lors du chargement: {str(e)}"))
            finally:
                self.db_manager.close()
        
        threading.Thread(target=load_in_background, daemon=True).start()
    
    def update_categories_display(self):
        """Mettre à jour l'affichage des catégories"""
        # Vider le conteneur
        self.categories_container.clear_widgets()
        
        if not self.categories_data:
            # Message si aucune catégorie
            no_data_label = MDLabel(
                text="📂 Aucune catégorie trouvée\n\nCliquez sur ➕ pour créer votre première catégorie",
                font_style="H6",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="200dp"
            )
            self.categories_container.add_widget(no_data_label)
        else:
            # Ajouter les cartes de catégories
            for category in self.categories_data:
                card = CategoryCard(
                    category_data=category,
                    on_edit_callback=self.edit_category,
                    on_delete_callback=self.confirm_delete_category
                )
                self.categories_container.add_widget(card)
    
    def add_category(self, *args):
        """Ajouter une nouvelle catégorie"""
        dialog = CategoryFormDialog(
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def edit_category(self, category_data):
        """Modifier une catégorie existante"""
        dialog = CategoryFormDialog(
            category_data=category_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def on_category_saved(self, category_data):
        """Callback appelé après sauvegarde d'une catégorie"""
        self.show_success(f"Catégorie '{category_data.get('nom', '')}' sauvegardée avec succès")
        self.refresh_categories()
    
    def confirm_delete_category(self, category_data):
        """Confirmer la suppression d'une catégorie"""
        products_count = category_data.get('products_count', 0)
        
        if products_count > 0:
            message = f"⚠️ Attention !\n\nLa catégorie '{category_data.get('nom', '')}' contient {products_count} produit(s).\n\nLa suppression de cette catégorie supprimera également tous les produits associés.\n\nÊtes-vous sûr de vouloir continuer ?"
        else:
            message = f"Êtes-vous sûr de vouloir supprimer la catégorie '{category_data.get('nom', '')}' ?"
        
        dialog = MDDialog(
            title="🗑️ Confirmer la suppression",
            text=message,
            buttons=[
                MDFlatButton(
                    text="❌ Annuler",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDRaisedButton(
                    text="🗑️ Supprimer",
                    on_release=lambda x: self.delete_category(category_data, dialog)
                )
            ]
        )
        dialog.open()
    
    def delete_category(self, category_data, dialog):
        """Supprimer une catégorie"""
        dialog.dismiss()
        
        def delete_in_background():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Impossible de se connecter à la base de données"))
                    return
                
                # Supprimer de la table categories
                success = self.db_manager.execute_update(
                    "DELETE FROM categories WHERE id = ?",
                    (category_data['id'],)
                )
                
                if success:
                    Clock.schedule_once(lambda dt: self.show_success(f"Catégorie '{category_data.get('nom', '')}' supprimée"))
                    Clock.schedule_once(lambda dt: self.refresh_categories())
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur lors de la suppression"))
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"))
            finally:
                self.db_manager.close()
        
        threading.Thread(target=delete_in_background, daemon=True).start()
    
    def refresh_categories(self, *args):
        """Actualiser la liste des catégories"""
        self.load_categories()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'main'