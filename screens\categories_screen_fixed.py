"""
Écran de gestion des catégories - Version avec formulaire corrigé pour champs visibles
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import Snackbar
from kivymd.app import MDApp
from kivy.clock import Clock
from kivy.metrics import dp
import threading
from database.db_manager import DatabaseManager


class VisibleCategoryFormDialog(MDDialog):
    """Formulaire avec champs garantis visibles pour créer/modifier une catégorie"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        super().__init__(
            title="✏️ Modifier la catégorie" if category_data else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.85, None),
            height="500dp",
            **kwargs
        )
        
        self.create_visible_form()
    
    def create_visible_form(self):
        """Créer le formulaire avec champs garantis visibles"""
        # Container principal avec fond coloré pour debug
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="400dp"
        )
        
        # Titre du formulaire
        title_label = MDLabel(
            text="📝 Informations de la catégorie",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="left"
        )
        main_container.add_widget(title_label)
        
        # Container pour les champs
        fields_container = MDBoxLayout(
            orientation='vertical',
            spacing="24dp",
            size_hint_y=None,
            height="280dp"
        )
        
        # Champ nom avec label séparé
        nom_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        nom_label = MDLabel(
            text="📂 Nom de la catégorie *",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Saisissez le nom de la catégorie",
            size_hint_y=None,
            height="56dp",
            # Mode rectangle pour meilleure visibilité
            mode="rectangle",
            # Couleurs explicites pour forcer la visibilité
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            # Propriétés supplémentaires
            required=True,
            max_text_length=100
        )
        
        nom_container.add_widget(nom_label)
        nom_container.add_widget(self.nom_field)
        
        # Champ description avec label séparé
        desc_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"
        )
        
        desc_label = MDLabel(
            text="📝 Description (optionnelle)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description détaillée de la catégorie",
            multiline=True,
            size_hint_y=None,
            height="88dp",
            # Mode rectangle pour meilleure visibilité
            mode="rectangle",
            # Couleurs explicites pour forcer la visibilité
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            # Propriétés supplémentaires
            max_text_length=500
        )
        
        desc_container.add_widget(desc_label)
        desc_container.add_widget(self.description_field)
        
        fields_container.add_widget(nom_container)
        fields_container.add_widget(desc_container)
        
        # Informations supplémentaires si modification
        if self.category_data and self.category_data.get('id'):
            info_container = MDBoxLayout(
                orientation='vertical',
                spacing="4dp",
                size_hint_y=None,
                height="60dp"
            )
            
            id_label = MDLabel(
                text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="20dp"
            )
            
            products_count = self.category_data.get('products_count', 0)
            products_label = MDLabel(
                text=f"📦 {products_count} produit(s) lié(s)",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="20dp"
            )
            
            date_creation = self.category_data.get('date_creation', '')
            if date_creation:
                try:
                    from datetime import datetime
                    if isinstance(date_creation, str):
                        date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                        date_formatted = date_obj.strftime('%d/%m/%Y')
                    else:
                        date_formatted = str(date_creation)[:10]
                except:
                    date_formatted = str(date_creation)[:10]
            else:
                date_formatted = "Non définie"
            
            date_label = MDLabel(
                text=f"📅 Créée le: {date_formatted}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="20dp"
            )
            
            info_container.add_widget(id_label)
            info_container.add_widget(products_label)
            info_container.add_widget(date_label)
            fields_container.add_widget(info_container)
        
        main_container.add_widget(fields_container)
        
        # Boutons d'action
        buttons_container = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="48dp"
        )
        
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            size_hint_x=0.4,
            on_release=self.dismiss
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            size_hint_x=0.6,
            on_release=self.save_category
        )
        
        buttons_container.add_widget(cancel_btn)
        buttons_container.add_widget(save_btn)
        
        main_container.add_widget(buttons_container)
        
        self.content_cls = main_container
    
    def save_category(self, *args):
        """Sauvegarder la catégorie avec validation"""
        nom = self.nom_field.text.strip()
        if not nom:
            self.show_error("Le nom de la catégorie est obligatoire")
            return
        
        if len(nom) < 2:
            self.show_error("Le nom doit contenir au moins 2 caractères")
            return
        
        description = self.description_field.text.strip()
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.category_data and self.category_data.get('id'):  # Modification
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ? AND id != ?",
                    (nom, self.category_data['id'])
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                success = self.db_manager.execute_update(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id'])
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    category_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            
            else:  # Création
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (nom,)
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                category_id = self.db_manager.execute_insert(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    category_data = {
                        'id': category_id,
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            if self.on_save_callback:
                self.on_save_callback(category_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {str(e)}")
        
        finally:
            self.db_manager.disconnect()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        Snackbar(
            text=f"❌ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=0.9
        ).open()
    
    def show_success(self, message):
        """Afficher un message de succès"""
        Snackbar(
            text=f"✅ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=0.9
        ).open()


# Test de la nouvelle classe
if __name__ == "__main__":
    import os
    import sys
    import warnings
    
    # Supprimer l'avertissement spécifique de KivyMD 1.2.0
    warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)
    
    # Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
    os.environ['KIVY_LOG_MODE'] = 'PYTHON'
    import logging
    logging.getLogger('kivy').setLevel(logging.ERROR)
    
    from kivymd.app import MDApp
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.button import MDRaisedButton
    
    class TestApp(MDApp):
        def build(self):
            screen = MDScreen()
            layout = MDBoxLayout(orientation='vertical', padding="20dp", spacing="20dp")
            
            btn = MDRaisedButton(
                text="Tester Formulaire Catégorie Corrigé",
                size_hint_y=None,
                height="50dp",
                on_release=self.open_form
            )
            
            layout.add_widget(btn)
            screen.add_widget(layout)
            return screen
        
        def open_form(self, *args):
            dialog = VisibleCategoryFormDialog()
            dialog.open()
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    TestApp().run()