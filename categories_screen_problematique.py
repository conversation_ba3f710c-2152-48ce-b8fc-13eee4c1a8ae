"""
Version avec le problème original - champs invisibles
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import <PERSON>Label
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import <PERSON><PERSON><PERSON>t<PERSON>ield
from kivymd.uix.scrollview import MDScroll<PERSON>iew
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import MDSnackbar
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
from database.db_manager import DatabaseManager


class CategoryFormDialog(MDDialog):
    """Formulaire avec le problème original - champs invisibles"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Créer les boutons d'abord pour MDDialog
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        super().__init__(
            title="✏️ Modifier la catégorie" if category_data else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.9, None),
            height="600dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
        
        self.create_form()
    
    def create_form(self):
        """Créer le formulaire - VERSION PROBLÉMATIQUE"""
        # Container principal
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            padding="20dp",
            size_hint_y=None,
            height="480dp"
        )
        
        # Container pour les champs
        fields_container = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="420dp"
        )
        
        # Champ nom - PROBLÈME: pas de label visible
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Nom de la catégorie",
            size_hint_y=None,
            height="56dp",
            # PROBLÈME: couleurs par défaut, souvent invisibles
        )
        
        # Champ description - PROBLÈME: pas de label visible
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description",
            multiline=True,
            size_hint_y=None,
            height="88dp",
            # PROBLÈME: couleurs par défaut, souvent invisibles
        )
        
        fields_container.add_widget(self.nom_field)
        fields_container.add_widget(self.description_field)
        
        # PROBLÈME: Méthode qui ne fonctionne pas correctement
        self.create_info_fields(fields_container)
        
        main_container.add_widget(fields_container)
        
        # PROBLÈME: Cette ligne est correcte mais le contenu est cassé
        self.content_cls = main_container
    
    def create_info_fields(self, parent_container):
        """MÉTHODE PROBLÉMATIQUE - ne fonctionne pas correctement"""
        
        # Cette méthode crée des champs mais ne les ajoute pas correctement
        # ou les ajoute de manière invisible
        
        if self.category_data and self.category_data.get('id'):
            # Champ ID
            id_field = MDTextField(
                text=str(self.category_data.get('id', 'Auto')),
                hint_text="ID automatique",
                size_hint_y=None,
                height="36dp",
                readonly=True,
                # PROBLÈME: couleurs qui rendent le champ invisible
            )
            
            # PROBLÈME: Ajout incorrect ou invisible
            parent_container.add_widget(id_field)
    
    def save_category(self, *args):
        """Sauvegarder la catégorie"""
        nom = self.nom_field.text.strip()
        if not nom:
            self.show_error("Le nom de la catégorie est obligatoire")
            return
        
        description = self.description_field.text.strip()
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.category_data and self.category_data.get('id'):  # Modification
                success = self.db_manager.execute_update(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id'])
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    category_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            
            else:  # Création
                category_id = self.db_manager.execute_insert(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    category_data = {
                        'id': category_id,
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            if self.on_save_callback:
                self.on_save_callback(category_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {str(e)}")
        
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"✅ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()


# Test de la version problématique
if __name__ == '__main__':
    from kivymd.app import MDApp
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.button import MDRaisedButton
    from kivymd.uix.boxlayout import MDBoxLayout
    
    class TestProblemeApp(MDApp):
        def build(self):
            screen = MDScreen()
            layout = MDBoxLayout(orientation='vertical', padding="20dp", spacing="20dp")
            
            btn = MDRaisedButton(
                text="Ouvrir Formulaire Problématique",
                on_release=self.open_dialog
            )
            layout.add_widget(btn)
            screen.add_widget(layout)
            return screen
        
        def open_dialog(self, *args):
            dialog = CategoryFormDialog()
            dialog.open()
    
    TestProblemeApp().run()