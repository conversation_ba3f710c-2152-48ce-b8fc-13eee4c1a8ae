#!/usr/bin/env python3
"""
Test pour vérifier la correction des champs invisibles du formulaire catégorie
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivy.app import App
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoryFormDialog


class TestCategoryFormApp(MDApp):
    """Application de test pour le formulaire de catégorie"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Formulaire Catégorie Corrigé"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test du Formulaire Catégorie Corrigé",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Cliquez sur les boutons ci-dessous pour tester les formulaires.\n"
                 "Vérifiez que les champs sont bien visibles et fonctionnels.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="➕ Tester Nouvelle Catégorie",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_new_category
        )
        
        # Test modification formulaire
        edit_btn = MDRaisedButton(
            text="✏️ Tester Modification Catégorie",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_edit_category
        )
        
        # Test avec données vides
        empty_btn = MDRaisedButton(
            text="🔍 Tester Formulaire Vide",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_empty_category
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(empty_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Résultats des tests s'afficheront ici...",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_category(self, *args):
        """Tester le formulaire de nouvelle catégorie"""
        self.result_label.text = "🧪 Test: Nouveau formulaire ouvert"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def test_edit_category(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "🧪 Test: Formulaire de modification ouvert"
        
        # Données de test
        test_data = {
            'id': 1,
            'nom': 'Électronique',
            'description': 'Produits électroniques et informatiques',
            'products_count': 5,
            'date_creation': '2024-01-15'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def test_empty_category(self, *args):
        """Tester avec des données vides"""
        self.result_label.text = "🧪 Test: Formulaire avec données vides ouvert"
        
        # Données vides
        empty_data = {
            'id': None,
            'nom': '',
            'description': '',
            'products_count': 0
        }
        
        dialog = CategoryFormDialog(
            category_data=empty_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def on_category_saved(self, category_data):
        """Callback quand une catégorie est sauvegardée"""
        self.result_label.text = f"✅ Catégorie sauvegardée: {category_data.get('nom', 'Sans nom')}"
        print(f"Catégorie sauvegardée: {category_data}")


def main():
    """Fonction principale"""
    print("🧪 Test du Formulaire Catégorie Corrigé")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestCategoryFormApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()