#!/usr/bin/env python3
"""
Test CRUD complet pour les catégories
"""

import os
import sys
import time

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, get_all_categories, add_category, update_category, delete_category, get_category_by_id

def test_crud_categories():
    """Test complet des opérations CRUD pour les catégories"""
    print("🧪 TEST CRUD COMPLET DES CATÉGORIES")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Base de données initialisée")
    
    try:
        # ==================== CREATE (Créer) ====================
        print("\n🔥 1. TEST CREATE - Création de catégories")
        print("-" * 40)
        
        # Créer plusieurs catégories de test
        categories_test = [
            {
                'nom': f'Test CRUD {int(time.time())}',
                'description': 'Catégorie de test pour CRUD - Création'
            },
            {
                'nom': f'Test Modification {int(time.time())}',
                'description': 'Catégorie qui sera modifiée'
            },
            {
                'nom': f'Test Suppression {int(time.time())}',
                'description': 'Catégorie qui sera supprimée'
            }
        ]
        
        created_ids = []
        for i, cat_data in enumerate(categories_test, 1):
            try:
                category_id = add_category(db_manager, cat_data)
                created_ids.append(category_id)
                print(f"✅ CREATE {i}: '{cat_data['nom']}' créée (ID: {category_id})")
            except Exception as e:
                print(f"❌ CREATE {i}: Erreur - {e}")
        
        print(f"📊 Total créées: {len(created_ids)}")
        
        # ==================== READ (Lire) ====================
        print("\n📖 2. TEST READ - Lecture des catégories")
        print("-" * 40)
        
        # Lire toutes les catégories
        all_categories = get_all_categories(db_manager)
        print(f"✅ READ ALL: {len(all_categories)} catégories trouvées")
        
        # Afficher les catégories créées
        for cat in all_categories:
            if cat['id'] in created_ids:
                print(f"   📂 {cat['nom']} - {cat.get('description', 'Pas de description')}")
        
        # Lire une catégorie spécifique par ID
        if created_ids:
            test_id = created_ids[0]
            specific_category = get_category_by_id(db_manager, test_id)
            if specific_category:
                print(f"✅ READ BY ID: Catégorie {test_id} trouvée - '{specific_category['nom']}'")
            else:
                print(f"❌ READ BY ID: Catégorie {test_id} non trouvée")
        
        # ==================== UPDATE (Modifier) ====================
        print("\n✏️ 3. TEST UPDATE - Modification des catégories")
        print("-" * 40)
        
        if len(created_ids) >= 2:
            update_id = created_ids[1]  # Prendre la deuxième catégorie créée
            
            # Données de modification
            update_data = {
                'nom': f'Test MODIFIÉ {int(time.time())}',
                'description': 'Catégorie modifiée avec succès par le test CRUD'
            }
            
            # Effectuer la modification
            success = update_category(db_manager, update_id, update_data)
            
            if success:
                print(f"✅ UPDATE: Catégorie {update_id} modifiée avec succès")
                
                # Vérifier la modification
                updated_category = get_category_by_id(db_manager, update_id)
                if updated_category:
                    print(f"   📂 Nouveau nom: '{updated_category['nom']}'")
                    print(f"   📝 Nouvelle description: '{updated_category['description']}'")
                else:
                    print("❌ UPDATE: Impossible de vérifier la modification")
            else:
                print(f"❌ UPDATE: Échec de la modification de la catégorie {update_id}")
        else:
            print("⚠️ UPDATE: Pas assez de catégories créées pour tester la modification")
        
        # ==================== DELETE (Supprimer) ====================
        print("\n🗑️ 4. TEST DELETE - Suppression des catégories")
        print("-" * 40)
        
        if len(created_ids) >= 3:
            delete_id = created_ids[2]  # Prendre la troisième catégorie créée
            
            # Vérifier qu'aucun produit n'utilise cette catégorie
            products_using = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
            """, (delete_id,))
            
            products_count = products_using[0]['count'] if products_using else 0
            print(f"📦 Produits utilisant la catégorie {delete_id}: {products_count}")
            
            # Effectuer la suppression
            success = delete_category(db_manager, delete_id)
            
            if success:
                print(f"✅ DELETE: Catégorie {delete_id} supprimée avec succès")
                
                # Vérifier la suppression
                deleted_category = get_category_by_id(db_manager, delete_id)
                if not deleted_category:
                    print("✅ DELETE: Suppression confirmée - catégorie introuvable")
                else:
                    print("❌ DELETE: La catégorie existe encore après suppression")
            else:
                print(f"❌ DELETE: Échec de la suppression de la catégorie {delete_id}")
        else:
            print("⚠️ DELETE: Pas assez de catégories créées pour tester la suppression")
        
        # ==================== TESTS AVANCÉS ====================
        print("\n🔬 5. TESTS AVANCÉS")
        print("-" * 40)
        
        # Test de contrainte d'unicité
        print("🔒 Test contrainte d'unicité...")
        try:
            duplicate_category = {
                'nom': 'Électronique',  # Nom qui existe déjà
                'description': 'Test de doublon'
            }
            add_category(db_manager, duplicate_category)
            print("❌ UNICITÉ: Doublon accepté (ne devrait pas arriver)")
        except Exception as e:
            print("✅ UNICITÉ: Contrainte respectée - doublon rejeté")
        
        # Test de suppression protégée
        print("\n🛡️ Test suppression protégée...")
        # Trouver une catégorie utilisée par des produits
        categories_with_products = db_manager.execute_query("""
            SELECT c.id, c.nom, COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN produits p ON c.id = p.categorie_id
            GROUP BY c.id, c.nom
            HAVING product_count > 0
            LIMIT 1
        """)
        
        if categories_with_products:
            protected_cat = categories_with_products[0]
            print(f"📦 Catégorie '{protected_cat['nom']}' utilisée par {protected_cat['product_count']} produit(s)")
            
            success = delete_category(db_manager, protected_cat['id'])
            if not success:
                print("✅ PROTECTION: Suppression bloquée - catégorie protégée")
            else:
                print("❌ PROTECTION: Suppression autorisée (ne devrait pas arriver)")
        else:
            print("⚠️ PROTECTION: Aucune catégorie utilisée trouvée pour le test")
        
        # ==================== RÉSUMÉ FINAL ====================
        print("\n📊 6. RÉSUMÉ FINAL")
        print("-" * 40)
        
        # Compter les catégories finales
        final_categories = get_all_categories(db_manager)
        print(f"📂 Total catégories finales: {len(final_categories)}")
        
        # Afficher les catégories par ordre alphabétique
        sorted_categories = sorted(final_categories, key=lambda x: x['nom'])
        print("\n📋 Liste des catégories:")
        for i, cat in enumerate(sorted_categories, 1):
            # Compter les produits dans cette catégorie
            products_in_cat = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
            """, (cat['id'],))
            product_count = products_in_cat[0]['count'] if products_in_cat else 0
            
            print(f"   {i:2d}. 📂 {cat['nom']:<25} ({product_count} produits)")
        
        print("\n🎉 TESTS CRUD TERMINÉS AVEC SUCCÈS !")
        print("=" * 50)
        print("✅ Fonctionnalités CRUD validées :")
        print("   • ➕ CREATE: Création de catégories")
        print("   • 📖 READ: Lecture individuelle et globale")
        print("   • ✏️ UPDATE: Modification des données")
        print("   • 🗑️ DELETE: Suppression sécurisée")
        print("   • 🔒 CONTRAINTES: Unicité et protection")
        print("   • 🛡️ SÉCURITÉ: Validation des données")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests CRUD: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    test_crud_categories()