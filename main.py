"""
Application de Gestion Commerciale
Développée avec Kivy et KivyMD
"""

import os
import sys
import warnings
import logging

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivymd.app import MDApp
from kivymd.theming import ThemableBehavior
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.navigationdrawer import MDNavigationDrawer
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.screen import MDScreen
from kivymd.uix.list import MDList, OneLineAvatarIconListItem, IconLeftWidget

# Import des modules de l'application
from database.db_manager import DatabaseManager
from screens.dashboard_screen import DashboardScreen
from screens.clients_screen import ClientsScreen
from screens.products_screen import ProductsScreen
from screens.categories_screen import CategoriesScreen
from screens.sales_screen import SalesScreen
from screens.reports_screen import ReportsScreen
from screens.settings_screen import SettingsScreen


class GesComApp(MDApp):
    """Application principale de gestion commerciale"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "GesComPro_LibTam - Gestion Commerciale | Powered by: LKAIHAL LAHCEN_AIA"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
        
        # Initialisation de la base de données
        self.db_manager = DatabaseManager()
        
        # Connexion et initialisation de la base de données
        if self.db_manager.connect():
            self.db_manager.initialize_database()
            print("Base de données initialisée avec succès")
        else:
            print("Erreur lors de l'initialisation de la base de données")
        
    def build(self):
        """Construction de l'interface utilisateur"""
        # Configuration du thème
        self.theme_cls.material_style = "M3"
        
        # Gestionnaire d'écrans
        self.screen_manager = ScreenManager()
        
        # Ajout des écrans
        self.screen_manager.add_widget(DashboardScreen(name='dashboard'))
        self.screen_manager.add_widget(ClientsScreen(name='clients'))
        self.screen_manager.add_widget(ProductsScreen(name='products'))
        self.screen_manager.add_widget(CategoriesScreen(name='categories'))
        self.screen_manager.add_widget(SalesScreen(name='sales'))
        self.screen_manager.add_widget(ReportsScreen(name='reports'))
        self.screen_manager.add_widget(SettingsScreen(name='settings'))
        
        # Layout principal
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils supérieure
        toolbar = MDTopAppBar(
            title="GesComPro_LibTam",
            elevation=2,
            left_action_items=[["menu", lambda x: self.toggle_nav_drawer()]],
            right_action_items=[
                ["cog", lambda x: self.go_to_settings()]
            ]
        )
        
        # Tiroir de navigation
        self.nav_drawer = self.create_navigation_drawer()
        
        # Ajout des composants au layout principal
        main_layout.add_widget(toolbar)
        main_layout.add_widget(self.screen_manager)
        
        # Layout avec tiroir de navigation
        nav_layout = MDBoxLayout()
        nav_layout.add_widget(self.nav_drawer)
        nav_layout.add_widget(main_layout)
        
        return nav_layout
    
    def create_navigation_drawer(self):
        """Création du tiroir de navigation"""
        nav_drawer = MDNavigationDrawer()
        
        # Menu de navigation
        nav_menu = MDList()
        
        # Items du menu
        menu_items = [
            {"icon": "view-dashboard", "text": "Tableau de bord", "screen": "dashboard"},
            {"icon": "account-group", "text": "Clients", "screen": "clients"},
            {"icon": "package-variant", "text": "Produits", "screen": "products"},
            {"icon": "folder", "text": "Catégories", "screen": "categories"},
            {"icon": "cart", "text": "Ventes", "screen": "sales"},
            {"icon": "chart-line", "text": "Rapports", "screen": "reports"},
            {"icon": "cog", "text": "Paramètres", "screen": "settings"},
        ]
        
        for item in menu_items:
            nav_item = OneLineAvatarIconListItem(
                text=item["text"],
                on_release=lambda x, screen=item["screen"]: self.go_to_screen(screen)
            )
            nav_item.add_widget(IconLeftWidget(icon=item["icon"]))
            nav_menu.add_widget(nav_item)
        
        # Option pour changer le thème
        theme_item = OneLineAvatarIconListItem(
            text="Changer le thème",
            on_release=lambda x: self.toggle_theme()
        )
        theme_item.add_widget(IconLeftWidget(icon="theme-light-dark"))
        nav_menu.add_widget(theme_item)
        
        nav_drawer.add_widget(nav_menu)
        return nav_drawer
    
    def toggle_nav_drawer(self):
        """Basculer l'affichage du tiroir de navigation"""
        self.nav_drawer.set_state("toggle")
    
    def toggle_theme(self):
        """Basculer entre thème clair et sombre"""
        if self.theme_cls.theme_style == "Light":
            self.theme_cls.theme_style = "Dark"
        else:
            self.theme_cls.theme_style = "Light"
    
    def go_to_screen(self, screen_name):
        """Naviguer vers un écran spécifique"""
        self.screen_manager.current = screen_name
        self.nav_drawer.set_state("close")
    
    def go_to_settings(self):
        """Aller aux paramètres"""
        self.go_to_screen('settings')
    
    def on_start(self):
        """Actions à effectuer au démarrage de l'application"""
        # Initialisation de la base de données
        self.db_manager.initialize_database()
        
        # Chargement des données initiales si nécessaire
        self.load_initial_data()
    
    def load_initial_data(self):
        """Chargement des données initiales"""
        # Vérifier si des données existent déjà
        if not self.db_manager.has_initial_data():
            # Créer des données de démonstration
            self.db_manager.create_sample_data()
    
    def on_stop(self):
        """Actions à effectuer à la fermeture de l'application"""
        # Fermeture propre de la base de données
        self.db_manager.close()


if __name__ == '__main__':
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    # Lancement de l'application
    GesComApp().run()