#!/usr/bin/env python3
"""
Widget de test rapide pour le formulaire catégories optimisé
"""

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager

class QuickTestApp(MDApp):
    def build(self):
        self.title = "Test Rapide Catégories Optimisées"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if not self.db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
        
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            padding="20dp",
            spacing="20dp"
        )
        
        title = MDLabel(
            text="🧪 Test Rapide Formulaire Catégories",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        test_btn = MDRaisedButton(
            text="📂 Tester Formulaire Catégories",
            size_hint_y=None,
            height="48dp",
            on_release=self.test_categories
        )
        
        self.result_label = MDLabel(
            text="Cliquez sur le bouton pour tester",
            font_style="Body1",
            halign="center",
            theme_text_color="Secondary"
        )
        
        layout.add_widget(title)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_categories(self, *args):
        """Tester le formulaire catégories"""
        try:
            from screens.categories_screen import CategoriesScreen
            
            # Créer l'écran de test
            categories_screen = CategoriesScreen()
            
            self.result_label.text = "✅ Formulaire catégories chargé avec succès!\nInterface moderne avec cartes et statistiques activée."
            
            print("✅ Test réussi: Formulaire catégories optimisé fonctionnel")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur: {str(e)}"
            print(f"❌ Erreur test: {e}")

if __name__ == "__main__":
    QuickTestApp().run()
