#!/usr/bin/env python3
"""
Test spécifique pour vérifier que les champs du formulaire sont visibles
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoryFormDialog


class TestChampsVisiblesApp(MDApp):
    """Application de test pour vérifier la visibilité des champs"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Champs Visibles Formulaire"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🔍 Test - Visibilité des Champs",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Ce test vérifie que les champs du formulaire sont visibles.\n\n"
                 "VÉRIFIEZ QUE VOUS VOYEZ :\n"
                 "✅ Champ 'Nom de la catégorie' avec bordure\n"
                 "✅ Champ 'Description' multiline avec bordure\n"
                 "✅ Labels au-dessus des champs\n"
                 "✅ Boutons 'Annuler' et 'Enregistrer' en bas\n\n"
                 "Si vous ne voyez que les boutons, le problème persiste !",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="200dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="140dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="🆕 Nouveau Formulaire",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_new_form
        )
        
        # Test modification
        edit_btn = MDRaisedButton(
            text="✏️ Formulaire Modification",
            size_hint_y=None,
            height="60dp",
            on_release=self.test_edit_form
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Cliquez sur un bouton pour ouvrir le formulaire.\n"
                 "Vérifiez que TOUS les champs sont visibles !",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le nouveau formulaire"""
        self.result_label.text = "🆕 NOUVEAU FORMULAIRE OUVERT\n\n" \
                                "VÉRIFIEZ QUE VOUS VOYEZ :\n" \
                                "✅ Titre 'Nouvelle catégorie'\n" \
                                "✅ Label 'Informations de la catégorie'\n" \
                                "✅ Champ 'Nom de la catégorie' avec bordure grise\n" \
                                "✅ Champ 'Description' multiline avec bordure grise\n" \
                                "✅ Boutons 'Annuler' et 'Enregistrer' en bas\n\n" \
                                "Si vous ne voyez que les boutons, le problème persiste !"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("🆕 Nouveau formulaire ouvert - Vérifiez la visibilité des champs !")
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "✏️ FORMULAIRE MODIFICATION OUVERT\n\n" \
                                "VÉRIFIEZ QUE VOUS VOYEZ :\n" \
                                "✅ Titre 'Modifier la catégorie'\n" \
                                "✅ Champs pré-remplis avec données\n" \
                                "✅ Informations supplémentaires (ID, produits, date)\n" \
                                "✅ Tous les champs avec bordures visibles\n\n" \
                                "Si vous ne voyez que les boutons, le problème persiste !"
        
        # Données de test
        test_data = {
            'id': 1,
            'nom': 'Test Électronique',
            'description': 'Catégorie de test pour vérifier la visibilité des champs dans le formulaire de modification.',
            'products_count': 3,
            'date_creation': '2024-01-15T10:30:00'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✏️ Formulaire de modification ouvert - Vérifiez la visibilité des champs !")
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        
        self.result_label.text = f"🎉 EXCELLENT ! LES CHAMPS ÉTAIENT VISIBLES !\n\n" \
                                f"Données saisies :\n" \
                                f"📂 Nom: {nom}\n" \
                                f"📝 Description: {description[:50]}{'...' if len(description) > 50 else ''}\n\n" \
                                f"✅ Le formulaire fonctionne parfaitement !\n" \
                                f"✅ Tous les champs sont visibles et fonctionnels !"
        
        print("🎉 SUCCÈS - Les champs étaient visibles et fonctionnels !")
        print(f"  - Nom saisi: {nom}")
        print(f"  - Description: {description}")


def main():
    """Fonction principale"""
    print("🔍 Test - Visibilité des Champs du Formulaire")
    print("=" * 60)
    print("OBJECTIF: Vérifier que les champs sont visibles dans le formulaire")
    print()
    print("PROBLÈME RAPPORTÉ:")
    print("❌ L'utilisateur ne voit que les boutons 'Annuler' et 'Enregistrer'")
    print("❌ Les champs 'Nom' et 'Description' ne sont pas visibles")
    print()
    print("SOLUTION APPLIQUÉE:")
    print("✅ Restructuration complète du formulaire")
    print("✅ Correction de la ligne self.content_cls = main_container")
    print("✅ Suppression des méthodes obsolètes")
    print("✅ Champs avec couleurs et bordures explicites")
    print()
    print("À VÉRIFIER MAINTENANT:")
    print("1. Ouvrir le formulaire")
    print("2. Vérifier que les champs sont visibles")
    print("3. Tester la saisie dans les champs")
    print("4. Vérifier que les boutons fonctionnent")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestChampsVisiblesApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()