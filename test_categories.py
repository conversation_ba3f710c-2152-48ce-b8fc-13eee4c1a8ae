#!/usr/bin/env python3
"""
Test des fonctionnalités de catégories
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, get_all_categories, add_category

def test_categories():
    """Tester les fonctionnalités de catégories"""
    print("🧪 TEST DES CATÉGORIES")
    print("=" * 40)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Base de données initialisée")
    
    try:
        # 1. Test d'ajout de catégorie
        print("\n1. Test d'ajout de catégorie...")
        test_category = {
            'nom': 'Test Catégorie',
            'description': 'Catégorie de test pour validation'
        }
        
        category_id = add_category(db_manager, test_category)
        print(f"✅ Catégorie ajoutée (ID: {category_id})")
        
        # 2. Test de récupération des catégories
        print("\n2. Test de récupération des catégories...")
        categories = get_all_categories(db_manager)
        print(f"✅ {len(categories)} catégories trouvées")
        
        for cat in categories:
            print(f"   📂 {cat['nom']} - {cat.get('description', 'Pas de description')}")
        
        # 3. Test de produits avec catégories
        print("\n3. Test de produits avec catégories...")
        produits_avec_categories = db_manager.execute_query("""
            SELECT p.nom, p.prix_vente, c.nom as categorie_nom
            FROM produits p
            LEFT JOIN categories c ON p.categorie_id = c.id
            WHERE p.actif = 1
            ORDER BY c.nom, p.nom
        """)
        
        print(f"✅ {len(produits_avec_categories)} produits trouvés")
        
        # Grouper par catégorie
        categories_produits = {}
        for produit in produits_avec_categories:
            cat_nom = produit.get('categorie_nom') or 'Sans catégorie'
            if cat_nom not in categories_produits:
                categories_produits[cat_nom] = []
            categories_produits[cat_nom].append(produit)
        
        for cat_nom, produits in categories_produits.items():
            print(f"\n📂 {cat_nom} ({len(produits)} produits):")
            for produit in produits[:3]:  # Afficher max 3 produits par catégorie
                print(f"   • {produit['nom']} - {produit['prix_vente']:.0f} DH")
            if len(produits) > 3:
                print(f"   ... et {len(produits) - 3} autres")
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("=" * 40)
        print("✅ Fonctionnalités testées :")
        print("   • Ajout de catégories")
        print("   • Récupération des catégories")
        print("   • Association produits-catégories")
        print("   • Affichage groupé par catégorie")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    test_categories()