"""
Écran de gestion des catégories - Version optimisée complète
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import Snackbar
from kivymd.app import MDApp
from kivy.clock import Clock
from kivy.metrics import dp
import threading
from database.db_manager import DatabaseManager


class CategoryCard(MDCard):
    """Carte optimisée pour afficher une catégorie"""
    
    def __init__(self, category_data, on_edit_callback, on_delete_callback, **kwargs):
        super().__init__(**kwargs)
        self.category_data = category_data
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "120dp"
        self.spacing = "8dp"
        self.radius = [8]
        
        layout = MDBoxLayout(orientation='vertical', spacing="8dp")
        
        # En-tête avec nom et actions
        header_layout = MDBoxLayout(
            orientation='horizontal', 
            size_hint_y=None, 
            height="40dp",
            spacing="8dp"
        )
        
        # Icône de catégorie
        icon_label = MDLabel(
            text="📂",
            font_style="H5",
            size_hint_x=None,
            width="40dp",
            halign="center"
        )
        
        # Nom de la catégorie
        nom_label = MDLabel(
            text=category_data.get('nom', 'Catégorie sans nom'),
            font_style="H6",
            theme_text_color="Primary",
            size_hint_x=0.6
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(
            orientation='horizontal', 
            size_hint_x=0.3, 
            spacing="4dp"
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Custom",
            icon_color=[0, 0.7, 0, 1],  # Vert
            on_release=lambda x: on_edit_callback(category_data)
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Custom",
            icon_color=[0.8, 0, 0, 1],  # Rouge
            on_release=lambda x: on_delete_callback(category_data)
        )
        
        actions_layout.add_widget(edit_btn)
        actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(icon_label)
        header_layout.add_widget(nom_label)
        header_layout.add_widget(actions_layout)
        
        # Description
        description_text = category_data.get('description', 'Aucune description')
        if len(description_text) > 80:
            description_text = description_text[:77] + "..."
        
        description_label = MDLabel(
            text=description_text,
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="30dp"
        )
        
        # Statistiques
        stats_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="30dp",
            spacing="16dp"
        )
        
        # Nombre de produits
        products_count = category_data.get('products_count', 0)
        products_label = MDLabel(
            text=f"📦 {products_count} produit(s)",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        # Date de création
        date_creation = category_data.get('date_creation', '')
        if date_creation:
            try:
                from datetime import datetime
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y')
                else:
                    date_formatted = str(date_creation)[:10]
            except:
                date_formatted = str(date_creation)[:10]
        else:
            date_formatted = "Non définie"
        
        date_label = MDLabel(
            text=f"📅 {date_formatted}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        stats_layout.add_widget(products_label)
        stats_layout.add_widget(date_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(description_label)
        layout.add_widget(stats_layout)
        
        self.add_widget(layout)


class CategoryFormDialog(MDDialog):
    """Formulaire optimisé pour créer/modifier une catégorie"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        super().__init__(
            title="✏️ Modifier la catégorie" if category_data else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.9, None),
            height="400dp",
            **kwargs
        )
        
        self.create_form()
    
    def create_form(self):
        """Créer le formulaire optimisé"""
        content_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="300dp",
            padding="16dp"
        )
        
        # Champ nom (obligatoire)
        self.nom_field = MDTextField(
            hint_text="📂 Nom de la catégorie *",
            text=self.category_data.get('nom', ''),
            required=True,
            helper_text="Nom unique pour identifier la catégorie",
            helper_text_mode="on_focus",
            max_text_length=100,
            size_hint_y=None,
            height="60dp"
        )
        
        # Champ description (optionnel)
        self.description_field = MDTextField(
            hint_text="📝 Description de la catégorie",
            text=self.category_data.get('description', ''),
            multiline=True,
            helper_text="Description détaillée (optionnelle)",
            helper_text_mode="on_focus",
            max_text_length=500,
            size_hint_y=None,
            height="100dp"
        )
        
        # Informations supplémentaires si modification
        if self.category_data:
            info_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height="60dp",
                spacing="4dp"
            )
            
            id_label = MDLabel(
                text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="20dp"
            )
            
            products_count = self.category_data.get('products_count', 0)
            products_label = MDLabel(
                text=f"📦 {products_count} produit(s) lié(s)",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="20dp"
            )
            
            info_layout.add_widget(id_label)
            info_layout.add_widget(products_label)
            content_layout.add_widget(info_layout)
        
        content_layout.add_widget(self.nom_field)
        content_layout.add_widget(self.description_field)
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="40dp"
        )
        
        cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss
        )
        
        save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        buttons_layout.add_widget(cancel_btn)
        buttons_layout.add_widget(save_btn)
        
        content_layout.add_widget(buttons_layout)
        
        self.content_cls = content_layout
    
    def save_category(self, *args):
        """Sauvegarder la catégorie avec validation"""
        nom = self.nom_field.text.strip()
        if not nom:
            self.show_error("Le nom de la catégorie est obligatoire")
            return
        
        if len(nom) < 2:
            self.show_error("Le nom doit contenir au moins 2 caractères")
            return
        
        description = self.description_field.text.strip()
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.category_data:  # Modification
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ? AND id != ?",
                    (nom, self.category_data['id'])
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                success = self.db_manager.execute_query(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id']),
                    commit=True
                )
                
                if success is not None:
                    self.show_success("Catégorie modifiée avec succès")
                    category_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            
            else:  # Création
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (nom,)
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                cursor = self.db_manager.connection.cursor()
                cursor.execute(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                self.db_manager.connection.commit()
                
                category_id = cursor.lastrowid
                self.show_success("Catégorie créée avec succès")
                
                category_data = {
                    'id': category_id,
                    'nom': nom,
                    'description': description
                }
            
            if self.on_save_callback:
                self.on_save_callback(category_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {str(e)}")
        
        finally:
            self.db_manager.disconnect()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        Snackbar(
            text=f"❌ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=(1 - (20 / 400))
        ).open()
    
    def show_success(self, message):
        """Afficher un message de succès"""
        Snackbar(
            text=f"✅ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=(1 - (20 / 400))
        ).open()


class CategoriesOptimizedScreen(MDScreen):
    """Écran optimisé de gestion des catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db_manager = DatabaseManager()
        self.categories_data = []
        self.create_interface()
        self.load_categories()
    
    def create_interface(self):
        """Créer l'interface optimisée"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📂 Gestion des Catégories",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[
                ["plus", lambda x: self.add_category()],
                ["refresh", lambda x: self.refresh_categories()]
            ],
            elevation=2
        )
        
        # Contenu principal
        content_layout = MDBoxLayout(
            orientation='vertical',
            padding="16dp",
            spacing="16dp"
        )
        
        # En-tête avec statistiques
        self.create_header(content_layout)
        
        # Zone de recherche
        self.create_search_bar(content_layout)
        
        # Liste des catégories
        self.create_categories_list(content_layout)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(content_layout)
        
        self.add_widget(main_layout)
    
    def create_header(self, parent_layout):
        """Créer l'en-tête avec statistiques"""
        header_card = MDCard(
            MDBoxLayout(
                orientation='horizontal',
                spacing="16dp",
                padding="16dp"
            ),
            size_hint_y=None,
            height="80dp",
            elevation=1,
            radius=[8]
        )
        
        stats_layout = MDBoxLayout(orientation='horizontal', spacing="32dp")
        
        self.total_categories_label = MDLabel(
            text="📂 0 catégorie(s)",
            font_style="H6",
            theme_text_color="Primary",
            halign="center"
        )
        
        self.categories_with_products_label = MDLabel(
            text="📦 0 avec produits",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        self.empty_categories_label = MDLabel(
            text="📭 0 vide(s)",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        stats_layout.add_widget(self.total_categories_label)
        stats_layout.add_widget(self.categories_with_products_label)
        stats_layout.add_widget(self.empty_categories_label)
        
        header_card.children[0].add_widget(stats_layout)
        parent_layout.add_widget(header_card)
    
    def create_search_bar(self, parent_layout):
        """Créer la barre de recherche optimisée"""
        search_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="60dp",
            spacing="8dp"
        )
        
        self.search_field = MDTextField(
            hint_text="🔍 Rechercher une catégorie...",
            size_hint_x=0.8,
            on_text=self.on_search_text
        )
        
        clear_btn = MDIconButton(
            icon="close",
            size_hint_x=0.1,
            on_release=self.clear_search
        )
        
        add_btn = MDRaisedButton(
            text="➕",
            size_hint_x=0.1,
            on_release=self.add_category
        )
        
        search_layout.add_widget(self.search_field)
        search_layout.add_widget(clear_btn)
        search_layout.add_widget(add_btn)
        
        parent_layout.add_widget(search_layout)
    
    def create_categories_list(self, parent_layout):
        """Créer la liste scrollable des catégories"""
        self.status_label = MDLabel(
            text="🔄 Chargement des catégories...",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        scroll = MDScrollView()
        
        self.categories_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            adaptive_height=True,
            padding=[0, "8dp", 0, "8dp"]
        )
        
        scroll.add_widget(self.categories_layout)
        
        parent_layout.add_widget(self.status_label)
        parent_layout.add_widget(scroll)
    
    def load_categories(self):
        """Charger les catégories de manière asynchrone"""
        def load_data():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Connexion DB impossible"), 0)
                    return
                
                categories = self.db_manager.execute_query("""
                    SELECT c.*, 
                           COUNT(p.id) as products_count
                    FROM categories c
                    LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                    GROUP BY c.id, c.nom, c.description, c.date_creation
                    ORDER BY c.nom ASC
                """)
                
                if categories is not None:
                    self.categories_data = categories
                    Clock.schedule_once(lambda dt: self.update_interface(), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur chargement catégories"), 0)
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"), 0)
            
            finally:
                self.db_manager.disconnect()
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_interface(self):
        """Mettre à jour l'interface avec les données"""
        self.categories_layout.clear_widgets()
        
        if not self.categories_data:
            self.status_label.text = "📭 Aucune catégorie trouvée"
            self.update_statistics(0, 0, 0)
            return
        
        # Filtrer selon la recherche
        search_text = getattr(self.search_field, 'text', '').lower().strip()
        filtered_categories = self.categories_data
        
        if search_text:
            filtered_categories = [
                cat for cat in self.categories_data
                if search_text in cat.get('nom', '').lower() or
                   search_text in cat.get('description', '').lower()
            ]
        
        if not filtered_categories:
            self.status_label.text = f"🔍 Aucune catégorie trouvée pour '{search_text}'"
            return
        
        self.status_label.text = f"📂 {len(filtered_categories)} catégorie(s) affichée(s)"
        
        for category in filtered_categories:
            card = CategoryCard(
                category_data=category,
                on_edit_callback=self.edit_category,
                on_delete_callback=self.delete_category
            )
            self.categories_layout.add_widget(card)
        
        # Mettre à jour les statistiques
        total = len(self.categories_data)
        with_products = len([cat for cat in self.categories_data if cat.get('products_count', 0) > 0])
        empty = total - with_products
        
        self.update_statistics(total, with_products, empty)
    
    def update_statistics(self, total, with_products, empty):
        """Mettre à jour les statistiques dans l'en-tête"""
        self.total_categories_label.text = f"📂 {total} catégorie(s)"
        self.categories_with_products_label.text = f"📦 {with_products} avec produits"
        self.empty_categories_label.text = f"📭 {empty} vide(s)"
    
    def on_search_text(self, instance, text):
        """Gérer la recherche en temps réel"""
        Clock.unschedule(self.delayed_search)
        Clock.schedule_once(self.delayed_search, 0.5)
    
    def delayed_search(self, dt):
        """Recherche avec délai"""
        self.update_interface()
    
    def clear_search(self, *args):
        """Effacer la recherche"""
        self.search_field.text = ""
        self.update_interface()
    
    def add_category(self, *args):
        """Ajouter une nouvelle catégorie"""
        dialog = CategoryFormDialog(
            category_data=None,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def edit_category(self, category_data):
        """Modifier une catégorie"""
        dialog = CategoryFormDialog(
            category_data=category_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def delete_category(self, category_data):
        """Supprimer une catégorie avec confirmation"""
        products_count = category_data.get('products_count', 0)
        
        if products_count > 0:
            message = f"⚠️ Cette catégorie contient {products_count} produit(s).\n\nLa suppression déplacera ces produits vers 'Aucune catégorie'.\n\nConfirmer la suppression ?"
        else:
            message = f"Confirmer la suppression de la catégorie '{category_data.get('nom', '')}'?"
        
        def confirm_delete(*args):
            self.perform_delete(category_data)
            confirm_dialog.dismiss()
        
        def cancel_delete(*args):
            confirm_dialog.dismiss()
        
        confirm_dialog = MDDialog(
            title="🗑️ Confirmer la suppression",
            text=message,
            buttons=[
                MDFlatButton(text="❌ Annuler", on_release=cancel_delete),
                MDRaisedButton(text="🗑️ Supprimer", on_release=confirm_delete)
            ]
        )
        confirm_dialog.open()
    
    def perform_delete(self, category_data):
        """Effectuer la suppression de la catégorie"""
        def delete_data():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Connexion DB impossible"), 0)
                    return
                
                # Déplacer les produits vers "aucune catégorie"
                self.db_manager.execute_query(
                    "UPDATE produits SET categorie_id = NULL WHERE categorie_id = ?",
                    (category_data['id'],),
                    commit=True
                )
                
                # Supprimer la catégorie
                result = self.db_manager.execute_query(
                    "DELETE FROM categories WHERE id = ?",
                    (category_data['id'],),
                    commit=True
                )
                
                if result is not None:
                    Clock.schedule_once(lambda dt: self.show_success("Catégorie supprimée avec succès"), 0)
                    Clock.schedule_once(lambda dt: self.refresh_categories(), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur lors de la suppression"), 0)
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"), 0)
            
            finally:
                self.db_manager.disconnect()
        
        threading.Thread(target=delete_data, daemon=True).start()
    
    def on_category_saved(self, category_data):
        """Callback après sauvegarde d'une catégorie"""
        self.refresh_categories()
    
    def refresh_categories(self, *args):
        """Actualiser la liste des catégories"""
        self.status_label.text = "🔄 Actualisation..."
        self.load_categories()
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'main'
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        Snackbar(
            text=f"❌ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=(1 - (20 / 400))
        ).open()
    
    def show_success(self, message):
        """Afficher un message de succès"""
        Snackbar(
            text=f"✅ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=(1 - (20 / 400))
        ).open()