#!/usr/bin/env python3
"""
Script d'intégration du formulaire catégories optimisé dans l'application principale
"""

import os
import sys
import shutil
from pathlib import Path

def integrer_formulaire_optimise():
    """Intégrer le formulaire optimisé dans l'application principale"""
    print("🔧 INTÉGRATION DU FORMULAIRE CATÉGORIES OPTIMISÉ")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    
    # 1. Sauvegarder l'ancien fichier
    print("💾 1. Sauvegarde de l'ancien fichier...")
    
    old_file = project_root / "screens" / "categories_screen.py"
    backup_file = project_root / "screens" / "categories_screen_backup.py"
    
    if old_file.exists():
        try:
            shutil.copy2(old_file, backup_file)
            print(f"✅ Sauvegarde créée: {backup_file}")
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde: {e}")
    
    # 2. Remplacer par la version optimisée
    print("\n🔄 2. Remplacement par la version optimisée...")
    
    optimized_file = project_root / "screens" / "categories_optimized.py"
    
    if optimized_file.exists():
        try:
            # Lire le contenu optimisé
            with open(optimized_file, 'r', encoding='utf-8') as f:
                optimized_content = f.read()
            
            # Remplacer le nom de la classe pour compatibilité
            optimized_content = optimized_content.replace(
                "class CategoriesOptimizedScreen(MDScreen):",
                "class CategoriesScreen(MDScreen):"
            )
            
            # Écrire dans le fichier principal
            with open(old_file, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            
            print(f"✅ Fichier remplacé: {old_file}")
            
        except Exception as e:
            print(f"❌ Erreur remplacement: {e}")
            return False
    else:
        print(f"❌ Fichier optimisé non trouvé: {optimized_file}")
        return False
    
    # 3. Mettre à jour l'application principale
    print("\n🔧 3. Mise à jour de l'application principale...")
    
    # Mettre à jour launch_optimized.py
    launch_file = project_root / "launch_optimized.py"
    
    if launch_file.exists():
        try:
            with open(launch_file, 'r', encoding='utf-8') as f:
                launch_content = f.read()
            
            # Ajouter l'import et la méthode pour les catégories si pas déjà présent
            if "def open_categories" not in launch_content:
                # Trouver la position pour ajouter la méthode
                insert_pos = launch_content.find("def open_reports(self, *args):")
                
                if insert_pos != -1:
                    categories_method = '''
    def open_categories(self, *args):
        """Ouvrir l'écran des catégories"""
        try:
            print("🔄 Chargement de l'écran catégories...")
            
            if not self.screen_manager.has_screen('categories'):
                from screens.categories_screen import CategoriesScreen
                categories_screen = CategoriesScreen(name='categories')
                self.screen_manager.add_widget(categories_screen)
                print("✅ Écran catégories créé")
            
            self.screen_manager.current = 'categories'
            print("✅ Écran catégories ouvert")
            
        except Exception as e:
            print(f"❌ Erreur ouverture catégories: {e}")
            self.create_temp_screen('categories', '📂 Catégories', 'Fonctionnalité en cours de développement')

'''
                    
                    # Insérer la méthode
                    launch_content = launch_content[:insert_pos] + categories_method + launch_content[insert_pos:]
                    
                    # Écrire le fichier modifié
                    with open(launch_file, 'w', encoding='utf-8') as f:
                        f.write(launch_content)
                    
                    print("✅ Méthode open_categories ajoutée à launch_optimized.py")
                else:
                    print("⚠️ Position d'insertion non trouvée dans launch_optimized.py")
            else:
                print("✅ Méthode open_categories déjà présente")
                
        except Exception as e:
            print(f"❌ Erreur mise à jour launch_optimized.py: {e}")
    
    # 4. Ajouter le bouton catégories au tableau de bord
    print("\n📊 4. Ajout du bouton au tableau de bord...")
    
    try:
        # Modifier create_main_screen dans launch_optimized.py
        with open(launch_file, 'r', encoding='utf-8') as f:
            launch_content = f.read()
        
        # Chercher la section des fonctionnalités
        functions_section = 'functions = ['
        
        if functions_section in launch_content:
            # Ajouter la fonction catégories si pas déjà présente
            if '"📂 Catégories"' not in launch_content:
                # Trouver la position pour insérer
                insert_pos = launch_content.find('("📊 Rapports", "Analyses et statistiques", self.open_reports)')
                
                if insert_pos != -1:
                    categories_function = '            ("📂 Catégories", "Gestion des catégories de produits", self.open_categories),\n            '
                    
                    launch_content = launch_content[:insert_pos] + categories_function + launch_content[insert_pos:]
                    
                    with open(launch_file, 'w', encoding='utf-8') as f:
                        f.write(launch_content)
                    
                    print("✅ Bouton catégories ajouté au tableau de bord")
                else:
                    print("⚠️ Position d'insertion du bouton non trouvée")
            else:
                print("✅ Bouton catégories déjà présent")
        else:
            print("⚠️ Section functions non trouvée")
            
    except Exception as e:
        print(f"❌ Erreur ajout bouton: {e}")
    
    # 5. Créer un script de test d'intégration
    print("\n🧪 5. Création du script de test d'intégration...")
    
    test_integration_content = '''#!/usr/bin/env python3
"""
Test d'intégration du formulaire catégories optimisé
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_integration():
    """Tester l'intégration du formulaire optimisé"""
    print("🧪 TEST D'INTÉGRATION FORMULAIRE CATÉGORIES")
    print("=" * 50)
    
    try:
        # Test 1: Import du module
        print("📦 Test 1: Import du module...")
        from screens.categories_screen import CategoriesScreen
        print("✅ Import réussi")
        
        # Test 2: Vérification de la classe
        print("🔍 Test 2: Vérification de la classe...")
        if hasattr(CategoriesScreen, 'create_interface'):
            print("✅ Méthode create_interface présente")
        else:
            print("❌ Méthode create_interface manquante")
        
        if hasattr(CategoriesScreen, 'load_categories'):
            print("✅ Méthode load_categories présente")
        else:
            print("❌ Méthode load_categories manquante")
        
        # Test 3: Test de la base de données
        print("🗄️ Test 3: Test de la base de données...")
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.connect():
            print("✅ Connexion DB réussie")
            
            # Test requête catégories
            categories = db_manager.execute_query("""
                SELECT c.*, COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id
                LIMIT 5
            """)
            
            if categories is not None:
                print(f"✅ Requête catégories réussie ({len(categories)} résultats)")
            else:
                print("❌ Erreur requête catégories")
            
            db_manager.disconnect()
        else:
            print("❌ Connexion DB échouée")
        
        # Test 4: Test de l'application principale
        print("🚀 Test 4: Test de l'application principale...")
        
        try:
            from launch_optimized import OptimizedGesComApp
            
            if hasattr(OptimizedGesComApp, 'open_categories'):
                print("✅ Méthode open_categories présente dans l'app principale")
            else:
                print("❌ Méthode open_categories manquante dans l'app principale")
                
        except ImportError as e:
            print(f"⚠️ Import application principale échoué: {e}")
        
        print("\\n🎉 TESTS D'INTÉGRATION TERMINÉS")
        print("✅ Le formulaire catégories optimisé est prêt à être utilisé")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_integration()
'''
    
    test_file = project_root / "test_integration_categories.py"
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_integration_content)
        print(f"✅ Script de test créé: {test_file}")
    except Exception as e:
        print(f"❌ Erreur création script de test: {e}")
    
    print("\n🎉 INTÉGRATION TERMINÉE")
    print("=" * 30)
    print("📋 Actions effectuées:")
    print("   1. ✅ Sauvegarde de l'ancien fichier")
    print("   2. ✅ Remplacement par la version optimisée")
    print("   3. ✅ Mise à jour de l'application principale")
    print("   4. ✅ Ajout du bouton au tableau de bord")
    print("   5. ✅ Création du script de test")
    
    print("\n🚀 Prochaines étapes:")
    print("   1. Exécuter: python test_integration_categories.py")
    print("   2. Lancer: python launch_optimized.py")
    print("   3. Tester le bouton '📂 Catégories' dans l'interface")
    
    return True

def restaurer_ancien_fichier():
    """Restaurer l'ancien fichier en cas de problème"""
    print("🔄 RESTAURATION DE L'ANCIEN FICHIER")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    
    old_file = project_root / "screens" / "categories_screen.py"
    backup_file = project_root / "screens" / "categories_screen_backup.py"
    
    if backup_file.exists():
        try:
            shutil.copy2(backup_file, old_file)
            print(f"✅ Fichier restauré: {old_file}")
            return True
        except Exception as e:
            print(f"❌ Erreur restauration: {e}")
            return False
    else:
        print(f"❌ Fichier de sauvegarde non trouvé: {backup_file}")
        return False

if __name__ == "__main__":
    print("🔧 SCRIPT D'INTÉGRATION FORMULAIRE CATÉGORIES OPTIMISÉ")
    print("=" * 70)
    
    choix = input("Choisissez une action:\\n1. Intégrer le formulaire optimisé\\n2. Restaurer l'ancien fichier\\nChoix (1 ou 2): ")
    
    if choix == "1":
        success = integrer_formulaire_optimise()
        if success:
            print("\\n🎉 Intégration réussie ! Le formulaire optimisé est maintenant actif.")
        else:
            print("\\n❌ Erreur lors de l'intégration.")
    elif choix == "2":
        success = restaurer_ancien_fichier()
        if success:
            print("\\n✅ Restauration réussie ! L'ancien fichier est restauré.")
        else:
            print("\\n❌ Erreur lors de la restauration.")
    else:
        print("❌ Choix invalide.")
    
    input("\\nAppuyez sur Entrée pour quitter...")