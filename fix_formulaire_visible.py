#!/usr/bin/env python3
"""
Correction du formulaire avec couleurs ultra-visibles
"""

# Patch pour forcer la visibilité des champs
def patch_category_form():
    """Appliquer un patch pour rendre les champs visibles"""
    
    import sys
    import os
    
    # Ajouter le chemin du projet
    project_path = r"d:\Apache24\htdocs\gescom"
    if project_path not in sys.path:
        sys.path.insert(0, project_path)
    
    # Importer et patcher la classe
    from screens.categories_screen import CategoryFormDialog
    
    # Sauvegarder la méthode originale
    original_create_form = CategoryFormDialog.create_form
    
    def create_form_visible(self):
        """Version patchée avec champs ultra-visibles"""
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.label import MDLabel
        from kivymd.uix.textfield import MDTextField
        
        print("🔧 PATCH: Création du formulaire avec champs ultra-visibles")
        
        # Container principal
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="480dp"
        )
        
        # Titre ultra-visible
        title_label = MDLabel(
            text="📝 FORMULAIRE CATÉGORIE - VERSION ULTRA-VISIBLE",
            font_style="H6",
            theme_text_color="Custom",
            text_color=[1, 0, 0, 1],  # Rouge vif
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        main_container.add_widget(title_label)
        
        # Container pour les champs
        fields_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="400dp"
        )
        
        # Label nom ultra-visible
        nom_label = MDLabel(
            text="📂 NOM DE LA CATÉGORIE (OBLIGATOIRE)",
            font_style="Subtitle1",
            theme_text_color="Custom",
            text_color=[0, 0, 1, 1],  # Bleu vif
            size_hint_y=None,
            height="30dp"
        )
        fields_container.add_widget(nom_label)
        
        # Champ nom ultra-visible
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Tapez le nom ici - CHAMP ULTRA-VISIBLE",
            size_hint_y=None,
            height="60dp",
            mode="rectangle",
            # Couleurs ultra-contrastées
            line_color_normal=[1, 0, 0, 1],      # Bordure rouge
            line_color_focus=[0, 1, 0, 1],       # Bordure verte au focus
            text_color_normal=[0, 0, 0, 1],      # Texte noir
            text_color_focus=[0, 0, 0, 1],       # Texte noir au focus
            hint_text_color_normal=[0.5, 0, 0.5, 1],  # Hint violet
            hint_text_color_focus=[0, 0.5, 0, 1],      # Hint vert au focus
            fill_color_normal=[1, 1, 0, 0.3],    # Fond jaune transparent
            fill_color_focus=[0, 1, 1, 0.3],     # Fond cyan au focus
            required=True,
            max_text_length=100
        )
        fields_container.add_widget(self.nom_field)
        
        # Label description ultra-visible
        desc_label = MDLabel(
            text="📝 DESCRIPTION (OPTIONNELLE)",
            font_style="Subtitle1",
            theme_text_color="Custom",
            text_color=[0, 0.5, 0, 1],  # Vert foncé
            size_hint_y=None,
            height="30dp"
        )
        fields_container.add_widget(desc_label)
        
        # Champ description ultra-visible
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description détaillée - CHAMP MULTILINE ULTRA-VISIBLE",
            multiline=True,
            size_hint_y=None,
            height="100dp",
            mode="rectangle",
            # Couleurs ultra-contrastées
            line_color_normal=[0, 0, 1, 1],      # Bordure bleue
            line_color_focus=[1, 0, 1, 1],       # Bordure magenta au focus
            text_color_normal=[0, 0, 0, 1],      # Texte noir
            text_color_focus=[0, 0, 0, 1],       # Texte noir au focus
            hint_text_color_normal=[0, 0, 0.5, 1],    # Hint bleu foncé
            hint_text_color_focus=[0.5, 0, 0.5, 1],   # Hint violet au focus
            fill_color_normal=[0.9, 0.9, 1, 0.5],     # Fond bleu clair
            fill_color_focus=[1, 0.9, 1, 0.5],        # Fond rose au focus
            max_text_length=500
        )
        fields_container.add_widget(self.description_field)
        
        # Informations supplémentaires si modification
        if self.category_data and self.category_data.get('id'):
            info_label = MDLabel(
                text=f"🆔 ID: {self.category_data.get('id')} | "
                     f"📦 {self.category_data.get('products_count', 0)} produits | "
                     f"📅 Créée le: {self.category_data.get('date_creation', 'N/A')[:10]}",
                font_style="Caption",
                theme_text_color="Custom",
                text_color=[0.5, 0.5, 0.5, 1],
                size_hint_y=None,
                height="40dp",
                halign="center"
            )
            fields_container.add_widget(info_label)
        
        # Message de confirmation
        confirm_label = MDLabel(
            text="✅ SI VOUS VOYEZ CE TEXTE ET LES CHAMPS CI-DESSUS,\nLE PROBLÈME EST RÉSOLU !",
            font_style="Body1",
            theme_text_color="Custom",
            text_color=[0, 0.7, 0, 1],  # Vert
            size_hint_y=None,
            height="60dp",
            halign="center"
        )
        fields_container.add_widget(confirm_label)
        
        main_container.add_widget(fields_container)
        
        # IMPORTANT: Assigner le contenu au dialog
        self.content_cls = main_container
        print("✅ PATCH: Formulaire ultra-visible créé et assigné")
    
    # Appliquer le patch
    CategoryFormDialog.create_form = create_form_visible
    print("🔧 PATCH appliqué à CategoryFormDialog.create_form")
    
    return CategoryFormDialog


if __name__ == '__main__':
    import os
    import sys
    import warnings
    
    # Supprimer l'avertissement spécifique de KivyMD 1.2.0
    warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)
    
    # Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
    os.environ['KIVY_LOG_MODE'] = 'PYTHON'
    import logging
    logging.getLogger('kivy').setLevel(logging.ERROR)
    
    from kivymd.app import MDApp
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.button import MDRaisedButton
    from kivymd.uix.label import MDLabel
    
    # Appliquer le patch
    CategoryFormDialog = patch_category_form()
    
    class TestPatchApp(MDApp):
        """Application de test avec patch ultra-visible"""
        
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
            self.title = "Test - Formulaire Ultra-Visible"
            self.theme_cls.theme_style = "Light"
            self.theme_cls.primary_palette = "Blue"
        
        def build(self):
            """Construction de l'interface"""
            screen = MDScreen()
            
            layout = MDBoxLayout(
                orientation='vertical',
                spacing="30dp",
                padding="30dp"
            )
            
            # Titre
            title = MDLabel(
                text="🔧 PATCH - Formulaire Ultra-Visible",
                font_style="H4",
                theme_text_color="Primary",
                halign="center",
                size_hint_y=None,
                height="80dp"
            )
            
            # Instructions
            instructions = MDLabel(
                text="Ce test utilise un PATCH avec des couleurs ultra-visibles.\n\n"
                     "VOUS DEVRIEZ VOIR :\n"
                     "🔴 Champ nom avec bordure ROUGE et fond JAUNE\n"
                     "🔵 Champ description avec bordure BLEUE et fond BLEU CLAIR\n"
                     "📝 Labels colorés en BLEU et VERT\n"
                     "✅ Message de confirmation en VERT\n\n"
                     "Si vous ne voyez toujours que les boutons,\n"
                     "il y a un problème plus profond !",
                font_style="Body1",
                theme_text_color="Secondary",
                halign="center",
                size_hint_y=None,
                height="200dp"
            )
            
            # Boutons de test
            buttons_layout = MDBoxLayout(
                orientation='vertical',
                spacing="20dp",
                size_hint_y=None,
                height="140dp"
            )
            
            new_btn = MDRaisedButton(
                text="🔧 Nouveau avec PATCH",
                size_hint_y=None,
                height="60dp",
                on_release=self.test_patch_new
            )
            
            edit_btn = MDRaisedButton(
                text="🔧 Modification avec PATCH",
                size_hint_y=None,
                height="60dp",
                on_release=self.test_patch_edit
            )
            
            buttons_layout.add_widget(new_btn)
            buttons_layout.add_widget(edit_btn)
            
            # Résultats
            self.result_label = MDLabel(
                text="Cliquez pour tester le formulaire avec PATCH ultra-visible.",
                font_style="Body2",
                theme_text_color="Secondary",
                halign="center"
            )
            
            layout.add_widget(title)
            layout.add_widget(instructions)
            layout.add_widget(buttons_layout)
            layout.add_widget(self.result_label)
            
            screen.add_widget(layout)
            return screen
        
        def test_patch_new(self, *args):
            """Tester le nouveau formulaire avec patch"""
            self.result_label.text = "🔧 FORMULAIRE AVEC PATCH OUVERT\n\n" \
                                    "Vérifiez que vous voyez maintenant :\n" \
                                    "🔴 Champ nom avec bordure rouge\n" \
                                    "🔵 Champ description avec bordure bleue\n" \
                                    "📝 Labels colorés\n" \
                                    "✅ Message de confirmation vert"
            
            dialog = CategoryFormDialog(
                on_save_callback=self.on_save_callback
            )
            dialog.open()
        
        def test_patch_edit(self, *args):
            """Tester la modification avec patch"""
            test_data = {
                'id': 1,
                'nom': 'Test PATCH',
                'description': 'Formulaire avec patch ultra-visible',
                'products_count': 5,
                'date_creation': '2024-01-15T10:30:00'
            }
            
            self.result_label.text = "🔧 MODIFICATION AVEC PATCH\n\n" \
                                    "Données pré-remplies :\n" \
                                    "📂 Nom: 'Test PATCH'\n" \
                                    "📝 Description: 'Formulaire avec patch...'\n" \
                                    "🆔 ID: 1, Produits: 5"
            
            dialog = CategoryFormDialog(
                category_data=test_data,
                on_save_callback=self.on_save_callback
            )
            dialog.open()
        
        def on_save_callback(self, category_data):
            """Callback de sauvegarde"""
            nom = category_data.get('nom', 'Sans nom')
            description = category_data.get('description', 'Aucune')
            
            self.result_label.text = f"🎉 PATCH RÉUSSI !\n\n" \
                                    f"Les champs étaient visibles et vous avez saisi :\n" \
                                    f"📂 Nom: {nom}\n" \
                                    f"📝 Description: {description[:40]}{'...' if len(description) > 40 else ''}\n\n" \
                                    f"✅ Le problème est résolu avec le patch !"
    
    print("🔧 Test du PATCH ultra-visible")
    print("=" * 50)
    print("Ce patch force des couleurs ultra-contrastées")
    print("pour garantir la visibilité des champs.")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestPatchApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test du patch: {e}")
        import traceback
        traceback.print_exc()