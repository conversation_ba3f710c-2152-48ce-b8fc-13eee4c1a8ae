"""
Écran de gestion des catégories - Version optimisée complète
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import MDSnackbar
from kivymd.app import MDApp
from kivy.clock import Clock
from kivy.metrics import dp
import threading
from database.db_manager import DatabaseManager


class CategoryCard(MDCard):
    """Carte optimisée pour afficher une catégorie"""
    
    def __init__(self, category_data, on_edit_callback, on_delete_callback, **kwargs):
        super().__init__(**kwargs)
        self.category_data = category_data
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "140dp"
        self.spacing = "8dp"
        self.radius = [8]
        
        layout = MDBoxLayout(orientation='vertical', spacing="8dp")
        
        # En-tête avec nom et actions
        header_layout = MDBoxLayout(
            orientation='horizontal', 
            size_hint_y=None, 
            height="40dp",
            spacing="8dp"
        )
        
        # Icône de catégorie
        icon_label = MDLabel(
            text="📂",
            font_style="H5",
            size_hint_x=None,
            width="40dp",
            halign="center"
        )
        
        # Nom de la catégorie
        nom_label = MDLabel(
            text=category_data.get('nom', 'Catégorie sans nom'),
            font_style="H6",
            theme_text_color="Primary",
            size_hint_x=0.6
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(
            orientation='horizontal', 
            size_hint_x=0.3, 
            spacing="4dp"
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Custom",
            icon_color=[0, 0.7, 0, 1],  # Vert
            on_release=lambda x: on_edit_callback(category_data)
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Custom",
            icon_color=[0.8, 0, 0, 1],  # Rouge
            on_release=lambda x: on_delete_callback(category_data)
        )
        
        actions_layout.add_widget(edit_btn)
        actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(icon_label)
        header_layout.add_widget(nom_label)
        header_layout.add_widget(actions_layout)
        
        # Description
        description_text = category_data.get('description', 'Aucune description')
        if len(description_text) > 80:
            description_text = description_text[:77] + "..."
        
        description_label = MDLabel(
            text=description_text,
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="30dp"
        )
        
        # Statistiques
        stats_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="30dp",
            spacing="16dp"
        )
        
        # Nombre de produits
        products_count = category_data.get('products_count', 0)
        products_label = MDLabel(
            text=f"📦 {products_count} produit(s)",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        # Date de création
        date_creation = category_data.get('date_creation', '')
        if date_creation:
            try:
                from datetime import datetime
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y')
                else:
                    date_formatted = str(date_creation)[:10]
            except:
                date_formatted = str(date_creation)[:10]
        else:
            date_formatted = "Non définie"
        
        date_label = MDLabel(
            text=f"📅 {date_formatted}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        stats_layout.add_widget(products_label)
        stats_layout.add_widget(date_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(description_label)
        layout.add_widget(stats_layout)
        
        self.add_widget(layout)


class CategoryFormDialog(MDDialog):
    """Formulaire de catégorie corrigé avec champs visibles"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Créer les boutons d'abord
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        super().__init__(
            title="✏️ Modifier la catégorie" if category_data else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.9, None),
            height="600dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
        
        self.create_form()
    
    def create_form(self):
        """Créer le formulaire avec champs garantis visibles"""
        # Container principal
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="480dp"
        )
        
        # Titre du formulaire
        title_label = MDLabel(
            text="📝 Informations de la catégorie",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="left"
        )
        main_container.add_widget(title_label)
        
        # Container pour les champs
        fields_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="400dp"
        )
        
        # Champ nom avec label séparé
        nom_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        nom_label = MDLabel(
            text="📂 Nom de la catégorie *",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Saisissez le nom de la catégorie",
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            required=True,
            max_text_length=100
        )
        
        nom_container.add_widget(nom_label)
        nom_container.add_widget(self.nom_field)
        
        # Champ description avec label séparé
        desc_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"
        )
        
        desc_label = MDLabel(
            text="📝 Description (optionnelle)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description détaillée de la catégorie",
            multiline=True,
            size_hint_y=None,
            height="88dp",
            mode="rectangle",
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            max_text_length=500
        )
        
        desc_container.add_widget(desc_label)
        desc_container.add_widget(self.description_field)
        
        fields_container.add_widget(nom_container)
        fields_container.add_widget(desc_container)
        
        # Informations supplémentaires si modification
        if self.category_data and self.category_data.get('id'):
            info_container = MDBoxLayout(
                orientation='vertical',
                spacing="8dp",
                size_hint_y=None,
                height="100dp"
            )
            
            # Ligne 1: ID et nombre de produits
            info_row1 = MDBoxLayout(
                orientation='horizontal',
                spacing="16dp",
                size_hint_y=None,
                height="30dp"
            )
            
            id_label = MDLabel(
                text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            
            products_count = self.category_data.get('products_count', 0)
            products_label = MDLabel(
                text=f"📦 {products_count} produit(s) lié(s)",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            
            info_row1.add_widget(id_label)
            info_row1.add_widget(products_label)
            
            # Ligne 2: Date de création
            date_creation = self.category_data.get('date_creation', '')
            if date_creation:
                try:
                    from datetime import datetime
                    if isinstance(date_creation, str):
                        date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                        date_formatted = date_obj.strftime('%d/%m/%Y à %H:%M')
                    else:
                        date_formatted = str(date_creation)[:16]
                except:
                    date_formatted = str(date_creation)[:16]
            else:
                date_formatted = "Non définie"
            
            date_label = MDLabel(
                text=f"📅 Créée le: {date_formatted}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="30dp"
            )
            
            info_container.add_widget(info_row1)
            info_container.add_widget(date_label)
            fields_container.add_widget(info_container)
        
        main_container.add_widget(fields_container)
        
        # IMPORTANT: Assigner le contenu au dialog
        self.content_cls = main_container
    
    def create_info_fields(self, parent_container):
        """Créer les champs d'informations supplémentaires"""
        
        # Champ ID (lecture seule si modification)
        id_container = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="60dp"
        )
        
        # ID
        id_field_container = MDBoxLayout(
            orientation='vertical',
            spacing="4dp",
            size_hint_x=0.5
        )
        
        id_label = MDLabel(
            text="🆔 ID de la catégorie",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        self.id_field = MDTextField(
            text=str(self.category_data.get('id', 'Nouveau')) if self.category_data.get('id') else 'Nouveau',
            hint_text="Généré automatiquement",
            size_hint_y=None,
            height="36dp",
            mode="rectangle",
            readonly=True,
            line_color_normal=[0.3, 0.3, 0.3, 1],
            line_color_focus=[0.3, 0.3, 0.3, 1],
            text_color_normal=[0.5, 0.5, 0.5, 1],
            fill_color_normal=[0.9, 0.9, 0.9, 1]
        )
        
        id_field_container.add_widget(id_label)
        id_field_container.add_widget(self.id_field)
        
        # Date de création
        date_field_container = MDBoxLayout(
            orientation='vertical',
            spacing="4dp",
            size_hint_x=0.5
        )
        
        date_label = MDLabel(
            text="📅 Date de création",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        # Formater la date
        date_creation = self.category_data.get('date_creation', '')
        if date_creation:
            try:
                from datetime import datetime
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                else:
                    date_formatted = str(date_creation)
            except:
                date_formatted = str(date_creation)
        else:
            from datetime import datetime
            date_formatted = datetime.now().strftime('%d/%m/%Y %H:%M')
        
        self.date_field = MDTextField(
            text=date_formatted,
            hint_text="Date automatique",
            size_hint_y=None,
            height="36dp",
            mode="rectangle",
            readonly=True,
            line_color_normal=[0.3, 0.3, 0.3, 1],
            line_color_focus=[0.3, 0.3, 0.3, 1],
            text_color_normal=[0.5, 0.5, 0.5, 1],
            fill_color_normal=[0.9, 0.9, 0.9, 1]
        )
        
        date_field_container.add_widget(date_label)
        date_field_container.add_widget(self.date_field)
        
        id_container.add_widget(id_field_container)
        id_container.add_widget(date_field_container)
        
        # Champ nombre de produits (lecture seule)
        products_container = MDBoxLayout(
            orientation='vertical',
            spacing="4dp",
            size_hint_y=None,
            height="60dp"
        )
        
        products_label = MDLabel(
            text="📦 Produits liés à cette catégorie",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        products_count = self.category_data.get('products_count', 0)
        products_text = f"{products_count} produit(s) lié(s)" if products_count > 0 else "Aucun produit lié"
        
        self.products_field = MDTextField(
            text=products_text,
            hint_text="Nombre de produits",
            size_hint_y=None,
            height="36dp",
            mode="rectangle",
            readonly=True,
            line_color_normal=[0.3, 0.3, 0.3, 1],
            line_color_focus=[0.3, 0.3, 0.3, 1],
            text_color_normal=[0.5, 0.5, 0.5, 1],
            fill_color_normal=[0.9, 0.9, 0.9, 1]
        )
        
        products_container.add_widget(products_label)
        products_container.add_widget(self.products_field)
        
        # Ajouter tous les containers
        parent_container.add_widget(id_container)
        parent_container.add_widget(products_container)
    
    def save_category(self, *args):
        """Sauvegarder la catégorie avec validation"""
        nom = self.nom_field.text.strip()
        if not nom:
            self.show_error("Le nom de la catégorie est obligatoire")
            return
        
        if len(nom) < 2:
            self.show_error("Le nom doit contenir au moins 2 caractères")
            return
        
        description = self.description_field.text.strip()
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.category_data and self.category_data.get('id'):  # Modification
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ? AND id != ?",
                    (nom, self.category_data['id'])
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                success = self.db_manager.execute_update(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id'])
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    category_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            
            else:  # Création
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (nom,)
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                category_id = self.db_manager.execute_insert(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    category_data = {
                        'id': category_id,
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            if self.on_save_callback:
                self.on_save_callback(category_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {str(e)}")
        
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"✅ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()


class CategoriesScreen(MDScreen):
    """Écran optimisé de gestion des catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db_manager = DatabaseManager()
        self.categories_data = []
        self.create_interface()
        self.load_categories()
    
    def create_interface(self):
        """Créer l'interface optimisée"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📂 Gestion des Catégories",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[
                ["plus", lambda x: self.add_category()],
                ["refresh", lambda x: self.refresh_categories()]
            ],
            elevation=2
        )
        
        # Contenu principal
        content_layout = MDBoxLayout(
            orientation='vertical',
            padding="16dp",
            spacing="16dp"
        )
        
        # En-tête avec statistiques
        self.create_header(content_layout)
        
        # Zone de recherche
        self.create_search_bar(content_layout)
        
        # Liste des catégories
        self.create_categories_list(content_layout)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(content_layout)
        
        self.add_widget(main_layout)
    
    def create_header(self, parent_layout):
        """Créer l'en-tête avec statistiques"""
        header_card = MDCard(
            MDBoxLayout(
                orientation='horizontal',
                spacing="16dp",
                padding="16dp"
            ),
            size_hint_y=None,
            height="80dp",
            elevation=1,
            radius=[8]
        )
        
        stats_layout = MDBoxLayout(orientation='horizontal', spacing="32dp")
        
        self.total_categories_label = MDLabel(
            text="📂 0 catégorie(s)",
            font_style="H6",
            theme_text_color="Primary",
            halign="center"
        )
        
        self.categories_with_products_label = MDLabel(
            text="📦 0 avec produits",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        self.empty_categories_label = MDLabel(
            text="📭 0 vide(s)",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center"
        )
        
        stats_layout.add_widget(self.total_categories_label)
        stats_layout.add_widget(self.categories_with_products_label)
        stats_layout.add_widget(self.empty_categories_label)
        
        header_card.children[0].add_widget(stats_layout)
        parent_layout.add_widget(header_card)
    
    def create_search_bar(self, parent_layout):
        """Créer la barre de recherche optimisée"""
        search_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="60dp",
            spacing="8dp"
        )
        
        self.search_field = MDTextField(
            hint_text="🔍 Rechercher une catégorie...",
            size_hint_x=0.8,
            on_text=self.on_search_text
        )
        
        clear_btn = MDIconButton(
            icon="close",
            size_hint_x=0.1,
            on_release=self.clear_search
        )
        
        add_btn = MDRaisedButton(
            text="➕",
            size_hint_x=0.1,
            on_release=self.add_category
        )
        
        search_layout.add_widget(self.search_field)
        search_layout.add_widget(clear_btn)
        search_layout.add_widget(add_btn)
        
        parent_layout.add_widget(search_layout)
    
    def create_categories_list(self, parent_layout):
        """Créer la liste scrollable des catégories"""
        self.status_label = MDLabel(
            text="🔄 Chargement des catégories...",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        scroll = MDScrollView()
        
        self.categories_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            adaptive_height=True,
            padding=[0, "8dp", 0, "8dp"]
        )
        
        scroll.add_widget(self.categories_layout)
        
        parent_layout.add_widget(self.status_label)
        parent_layout.add_widget(scroll)
    
    def load_categories(self):
        """Charger les catégories de manière asynchrone"""
        def load_data():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Connexion DB impossible"), 0)
                    return
                
                categories = self.db_manager.execute_query("""
                    SELECT c.*, 
                           COUNT(p.id) as products_count
                    FROM categories c
                    LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                    GROUP BY c.id, c.nom, c.description, c.date_creation
                    ORDER BY c.nom ASC
                """)
                
                if categories is not None:
                    self.categories_data = categories
                    Clock.schedule_once(lambda dt: self.update_interface(), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur chargement catégories"), 0)
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"), 0)
            
            finally:
                self.db_manager.disconnect()
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def update_interface(self):
        """Mettre à jour l'interface avec les données"""
        self.categories_layout.clear_widgets()
        
        if not self.categories_data:
            self.status_label.text = "📭 Aucune catégorie trouvée"
            self.update_statistics(0, 0, 0)
            return
        
        # Filtrer selon la recherche
        search_text = getattr(self.search_field, 'text', '').lower().strip()
        filtered_categories = self.categories_data
        
        if search_text:
            filtered_categories = [
                cat for cat in self.categories_data
                if search_text in cat.get('nom', '').lower() or
                   search_text in cat.get('description', '').lower()
            ]
        
        if not filtered_categories:
            self.status_label.text = f"🔍 Aucune catégorie trouvée pour '{search_text}'"
            return
        
        self.status_label.text = f"📂 {len(filtered_categories)} catégorie(s) affichée(s)"
        
        for category in filtered_categories:
            card = CategoryCard(
                category_data=category,
                on_edit_callback=self.edit_category,
                on_delete_callback=self.delete_category
            )
            self.categories_layout.add_widget(card)
        
        # Mettre à jour les statistiques
        total = len(self.categories_data)
        with_products = len([cat for cat in self.categories_data if cat.get('products_count', 0) > 0])
        empty = total - with_products
        
        self.update_statistics(total, with_products, empty)
    
    def update_statistics(self, total, with_products, empty):
        """Mettre à jour les statistiques dans l'en-tête"""
        self.total_categories_label.text = f"📂 {total} catégorie(s)"
        self.categories_with_products_label.text = f"📦 {with_products} avec produits"
        self.empty_categories_label.text = f"📭 {empty} vide(s)"
    
    def on_search_text(self, instance, text):
        """Gérer la recherche en temps réel"""
        Clock.unschedule(self.delayed_search)
        Clock.schedule_once(self.delayed_search, 0.5)
    
    def delayed_search(self, dt):
        """Recherche avec délai"""
        self.update_interface()
    
    def clear_search(self, *args):
        """Effacer la recherche"""
        self.search_field.text = ""
        self.update_interface()
    
    def add_category(self, *args):
        """Ajouter une nouvelle catégorie"""
        dialog = CategoryFormDialog(
            category_data=None,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def edit_category(self, category_data):
        """Modifier une catégorie"""
        dialog = CategoryFormDialog(
            category_data=category_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def delete_category(self, category_data):
        """Supprimer une catégorie avec confirmation"""
        products_count = category_data.get('products_count', 0)
        
        if products_count > 0:
            message = f"⚠️ Cette catégorie contient {products_count} produit(s).\n\nLa suppression déplacera ces produits vers 'Aucune catégorie'.\n\nConfirmer la suppression ?"
        else:
            message = f"Confirmer la suppression de la catégorie '{category_data.get('nom', '')}'?"
        
        def confirm_delete(*args):
            self.perform_delete(category_data)
            confirm_dialog.dismiss()
        
        def cancel_delete(*args):
            confirm_dialog.dismiss()
        
        confirm_dialog = MDDialog(
            title="🗑️ Confirmer la suppression",
            text=message,
            buttons=[
                MDFlatButton(text="❌ Annuler", on_release=cancel_delete),
                MDRaisedButton(text="🗑️ Supprimer", on_release=confirm_delete)
            ]
        )
        confirm_dialog.open()
    
    def perform_delete(self, category_data):
        """Effectuer la suppression de la catégorie"""
        def delete_data():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Connexion DB impossible"), 0)
                    return
                
                # Déplacer les produits vers "aucune catégorie"
                self.db_manager.execute_query(
                    "UPDATE produits SET categorie_id = NULL WHERE categorie_id = ?",
                    (category_data['id'],),
                    commit=True
                )
                
                # Supprimer la catégorie
                result = self.db_manager.execute_query(
                    "DELETE FROM categories WHERE id = ?",
                    (category_data['id'],),
                    commit=True
                )
                
                if result is not None:
                    Clock.schedule_once(lambda dt: self.show_success("Catégorie supprimée avec succès"), 0)
                    Clock.schedule_once(lambda dt: self.refresh_categories(), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_error("Erreur lors de la suppression"), 0)
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur: {str(e)}"), 0)
            
            finally:
                self.db_manager.disconnect()
        
        threading.Thread(target=delete_data, daemon=True).start()
    
    def on_category_saved(self, category_data):
        """Callback après sauvegarde d'une catégorie"""
        self.refresh_categories()
    
    def refresh_categories(self, *args):
        """Actualiser la liste des catégories"""
        self.status_label.text = "🔄 Actualisation..."
        self.load_categories()
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'main'
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        Snackbar(
            text=f"❌ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=(1 - (20 / 400))
        ).open()
    
    def show_success(self, message):
        """Afficher un message de succès"""
        Snackbar(
            text=f"✅ {message}",
            snackbar_x="10dp",
            snackbar_y="10dp",
            size_hint_x=(1 - (20 / 400))
        ).open()