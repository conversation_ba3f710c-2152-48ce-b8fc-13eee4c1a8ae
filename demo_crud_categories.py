#!/usr/bin/env python3
"""
Démonstration interactive du CRUD des catégories
"""

import os
import sys
import time

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, get_all_categories, add_category, update_category, delete_category, get_category_by_id

def demo_crud_interactif():
    """Démonstration interactive du CRUD des catégories"""
    print("🎮 DÉMONSTRATION CRUD INTERACTIVE DES CATÉGORIES")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Base de données initialisée")
    print("\n🎯 Cette démonstration va vous montrer toutes les opérations CRUD :")
    print("   • ➕ CREATE : Créer de nouvelles catégories")
    print("   • 📖 READ   : Lire et afficher les catégories")
    print("   • ✏️ UPDATE : Modifier les catégories existantes")
    print("   • 🗑️ DELETE : Supprimer les catégories")
    
    try:
        while True:
            print("\n" + "="*60)
            print("🎮 MENU CRUD INTERACTIF")
            print("="*60)
            print("1. ➕ CREATE - Créer une nouvelle catégorie")
            print("2. 📖 READ   - Afficher toutes les catégories")
            print("3. 📋 READ   - Afficher une catégorie par ID")
            print("4. ✏️ UPDATE - Modifier une catégorie")
            print("5. 🗑️ DELETE - Supprimer une catégorie")
            print("6. 🧪 TEST   - Test CRUD automatique")
            print("7. 📊 STATS  - Statistiques des catégories")
            print("0. 🚪 QUITTER")
            print("="*60)
            
            choix = input("👉 Votre choix (0-7): ").strip()
            
            if choix == "0":
                print("\n👋 Au revoir ! Merci d'avoir testé le CRUD des catégories.")
                break
            
            elif choix == "1":
                # CREATE
                print("\n➕ CRÉATION D'UNE NOUVELLE CATÉGORIE")
                print("-" * 40)
                
                nom = input("📝 Nom de la catégorie: ").strip()
                if not nom:
                    print("❌ Le nom est obligatoire !")
                    continue
                
                description = input("📄 Description (optionnelle): ").strip()
                
                category_data = {
                    'nom': nom,
                    'description': description
                }
                
                try:
                    category_id = add_category(db_manager, category_data)
                    print(f"✅ Catégorie '{nom}' créée avec succès (ID: {category_id})")
                except Exception as e:
                    print(f"❌ Erreur lors de la création: {e}")
            
            elif choix == "2":
                # READ ALL
                print("\n📖 AFFICHAGE DE TOUTES LES CATÉGORIES")
                print("-" * 40)
                
                categories = get_all_categories(db_manager)
                
                if not categories:
                    print("📂 Aucune catégorie trouvée.")
                else:
                    print(f"📊 {len(categories)} catégorie(s) trouvée(s):\n")
                    
                    for i, cat in enumerate(categories, 1):
                        # Compter les produits
                        products = db_manager.execute_query("""
                            SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
                        """, (cat['id'],))
                        product_count = products[0]['count'] if products else 0
                        
                        print(f"{i:2d}. 📂 ID: {cat['id']:<3} | {cat['nom']:<25} | {product_count} produits")
                        if cat.get('description'):
                            print(f"     📝 {cat['description']}")
                        print(f"     📅 Créée: {cat.get('date_creation', 'Inconnue')}")
                        print()
            
            elif choix == "3":
                # READ BY ID
                print("\n📋 AFFICHAGE D'UNE CATÉGORIE PAR ID")
                print("-" * 40)
                
                try:
                    category_id = int(input("🔢 ID de la catégorie: "))
                    category = get_category_by_id(db_manager, category_id)
                    
                    if category:
                        print(f"\n✅ Catégorie trouvée:")
                        print(f"📂 ID: {category['id']}")
                        print(f"📝 Nom: {category['nom']}")
                        print(f"📄 Description: {category.get('description', 'Pas de description')}")
                        print(f"📅 Date création: {category.get('date_creation', 'Inconnue')}")
                        
                        # Compter les produits
                        products = db_manager.execute_query("""
                            SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
                        """, (category_id,))
                        product_count = products[0]['count'] if products else 0
                        print(f"📦 Produits: {product_count}")
                    else:
                        print(f"❌ Aucune catégorie trouvée avec l'ID {category_id}")
                        
                except ValueError:
                    print("❌ ID invalide ! Veuillez entrer un nombre.")
            
            elif choix == "4":
                # UPDATE
                print("\n✏️ MODIFICATION D'UNE CATÉGORIE")
                print("-" * 40)
                
                # Afficher les catégories disponibles
                categories = get_all_categories(db_manager)
                if not categories:
                    print("❌ Aucune catégorie à modifier.")
                    continue
                
                print("📋 Catégories disponibles:")
                for cat in categories:
                    print(f"   {cat['id']}. {cat['nom']}")
                
                try:
                    category_id = int(input("\n🔢 ID de la catégorie à modifier: "))
                    category = get_category_by_id(db_manager, category_id)
                    
                    if not category:
                        print(f"❌ Catégorie avec ID {category_id} introuvable.")
                        continue
                    
                    print(f"\n📂 Catégorie actuelle: {category['nom']}")
                    print(f"📄 Description actuelle: {category.get('description', 'Pas de description')}")
                    
                    nouveau_nom = input(f"\n📝 Nouveau nom (actuel: {category['nom']}): ").strip()
                    nouvelle_desc = input(f"📄 Nouvelle description (actuelle: {category.get('description', '')}): ").strip()
                    
                    if not nouveau_nom:
                        nouveau_nom = category['nom']
                    
                    update_data = {
                        'nom': nouveau_nom,
                        'description': nouvelle_desc
                    }
                    
                    success = update_category(db_manager, category_id, update_data)
                    
                    if success:
                        print(f"✅ Catégorie {category_id} modifiée avec succès !")
                    else:
                        print(f"❌ Échec de la modification de la catégorie {category_id}")
                        
                except ValueError:
                    print("❌ ID invalide ! Veuillez entrer un nombre.")
                except Exception as e:
                    print(f"❌ Erreur lors de la modification: {e}")
            
            elif choix == "5":
                # DELETE
                print("\n🗑️ SUPPRESSION D'UNE CATÉGORIE")
                print("-" * 40)
                
                # Afficher les catégories disponibles
                categories = get_all_categories(db_manager)
                if not categories:
                    print("❌ Aucune catégorie à supprimer.")
                    continue
                
                print("📋 Catégories disponibles:")
                for cat in categories:
                    # Compter les produits
                    products = db_manager.execute_query("""
                        SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
                    """, (cat['id'],))
                    product_count = products[0]['count'] if products else 0
                    
                    status = "🛡️ PROTÉGÉE" if product_count > 0 else "🗑️ Supprimable"
                    print(f"   {cat['id']}. {cat['nom']} ({product_count} produits) - {status}")
                
                try:
                    category_id = int(input("\n🔢 ID de la catégorie à supprimer: "))
                    category = get_category_by_id(db_manager, category_id)
                    
                    if not category:
                        print(f"❌ Catégorie avec ID {category_id} introuvable.")
                        continue
                    
                    # Vérifier les produits
                    products = db_manager.execute_query("""
                        SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
                    """, (category_id,))
                    product_count = products[0]['count'] if products else 0
                    
                    if product_count > 0:
                        print(f"🛡️ Impossible de supprimer '{category['nom']}' : {product_count} produit(s) l'utilisent.")
                        continue
                    
                    # Confirmation
                    confirmation = input(f"\n⚠️ Êtes-vous sûr de vouloir supprimer '{category['nom']}' ? (oui/non): ").strip().lower()
                    
                    if confirmation in ['oui', 'o', 'yes', 'y']:
                        success = delete_category(db_manager, category_id)
                        
                        if success:
                            print(f"✅ Catégorie '{category['nom']}' supprimée avec succès !")
                        else:
                            print(f"❌ Échec de la suppression de la catégorie '{category['nom']}'")
                    else:
                        print("❌ Suppression annulée.")
                        
                except ValueError:
                    print("❌ ID invalide ! Veuillez entrer un nombre.")
                except Exception as e:
                    print(f"❌ Erreur lors de la suppression: {e}")
            
            elif choix == "6":
                # TEST AUTOMATIQUE
                print("\n🧪 TEST CRUD AUTOMATIQUE")
                print("-" * 40)
                
                test_name = f"Test Auto {int(time.time())}"
                
                try:
                    # CREATE
                    print("1️⃣ CREATE - Création...")
                    test_category = {
                        'nom': test_name,
                        'description': 'Catégorie créée par le test automatique'
                    }
                    category_id = add_category(db_manager, test_category)
                    print(f"   ✅ Catégorie '{test_name}' créée (ID: {category_id})")
                    
                    # READ
                    print("2️⃣ READ - Lecture...")
                    created_category = get_category_by_id(db_manager, category_id)
                    if created_category:
                        print(f"   ✅ Catégorie lue: '{created_category['nom']}'")
                    
                    # UPDATE
                    print("3️⃣ UPDATE - Modification...")
                    update_data = {
                        'nom': f"{test_name} MODIFIÉ",
                        'description': 'Catégorie modifiée par le test automatique'
                    }
                    success = update_category(db_manager, category_id, update_data)
                    if success:
                        print(f"   ✅ Catégorie modifiée")
                    
                    # DELETE
                    print("4️⃣ DELETE - Suppression...")
                    success = delete_category(db_manager, category_id)
                    if success:
                        print(f"   ✅ Catégorie supprimée")
                    
                    print("\n🎉 Test CRUD automatique terminé avec succès !")
                    
                except Exception as e:
                    print(f"❌ Erreur lors du test automatique: {e}")
            
            elif choix == "7":
                # STATISTIQUES
                print("\n📊 STATISTIQUES DES CATÉGORIES")
                print("-" * 40)
                
                categories = get_all_categories(db_manager)
                
                if not categories:
                    print("📂 Aucune catégorie trouvée.")
                    continue
                
                print(f"📊 Total catégories: {len(categories)}")
                
                # Statistiques par catégorie
                categories_with_products = []
                categories_empty = []
                total_products = 0
                
                for cat in categories:
                    products = db_manager.execute_query("""
                        SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
                    """, (cat['id'],))
                    product_count = products[0]['count'] if products else 0
                    
                    if product_count > 0:
                        categories_with_products.append((cat, product_count))
                        total_products += product_count
                    else:
                        categories_empty.append(cat)
                
                print(f"📦 Total produits catégorisés: {total_products}")
                print(f"📂 Catégories avec produits: {len(categories_with_products)}")
                print(f"📭 Catégories vides: {len(categories_empty)}")
                
                if categories_with_products:
                    print(f"\n🏆 Top catégories par nombre de produits:")
                    sorted_cats = sorted(categories_with_products, key=lambda x: x[1], reverse=True)
                    for i, (cat, count) in enumerate(sorted_cats[:5], 1):
                        print(f"   {i}. {cat['nom']}: {count} produits")
                
                if categories_empty:
                    print(f"\n📭 Catégories vides:")
                    for cat in categories_empty[:5]:
                        print(f"   • {cat['nom']}")
                    if len(categories_empty) > 5:
                        print(f"   ... et {len(categories_empty) - 5} autres")
            
            else:
                print("❌ Choix invalide ! Veuillez choisir entre 0 et 7.")
            
            # Pause avant le prochain menu
            input("\n⏸️ Appuyez sur Entrée pour continuer...")
    
    except KeyboardInterrupt:
        print("\n\n👋 Démonstration interrompue par l'utilisateur.")
    
    except Exception as e:
        print(f"❌ Erreur lors de la démonstration: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    demo_crud_interactif()