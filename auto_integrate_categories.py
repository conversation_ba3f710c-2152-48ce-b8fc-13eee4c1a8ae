#!/usr/bin/env python3
"""
Intégration automatique du formulaire catégories optimisé
"""

import os
import sys
import shutil
from pathlib import Path

def auto_integrate():
    """Intégration automatique du formulaire optimisé"""
    print("🚀 INTÉGRATION AUTOMATIQUE DU FORMULAIRE CATÉGORIES OPTIMISÉ")
    print("=" * 70)
    
    project_root = Path(__file__).parent
    
    # 1. Remplacer le fichier categories_screen.py
    print("🔄 1. Remplacement du fichier categories_screen.py...")
    
    old_file = project_root / "screens" / "categories_screen.py"
    optimized_file = project_root / "screens" / "categories_optimized.py"
    
    if optimized_file.exists():
        try:
            # Lire le contenu optimisé
            with open(optimized_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Remplacer le nom de la classe pour compatibilité
            content = content.replace(
                "class CategoriesOptimizedScreen(MDScreen):",
                "class CategoriesScreen(MDScreen):"
            )
            
            # Écrire dans le fichier principal
            with open(old_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Fichier categories_screen.py mis à jour avec la version optimisée")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    else:
        print(f"❌ Fichier optimisé non trouvé: {optimized_file}")
        return False
    
    # 2. Créer un widget de test rapide
    print("\n🧪 2. Création d'un widget de test rapide...")
    
    test_widget_content = '''#!/usr/bin/env python3
"""
Widget de test rapide pour le formulaire catégories optimisé
"""

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from database.db_manager import DatabaseManager

class QuickTestApp(MDApp):
    def build(self):
        self.title = "Test Rapide Catégories Optimisées"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if not self.db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
        
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            padding="20dp",
            spacing="20dp"
        )
        
        title = MDLabel(
            text="🧪 Test Rapide Formulaire Catégories",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        test_btn = MDRaisedButton(
            text="📂 Tester Formulaire Catégories",
            size_hint_y=None,
            height="48dp",
            on_release=self.test_categories
        )
        
        self.result_label = MDLabel(
            text="Cliquez sur le bouton pour tester",
            font_style="Body1",
            halign="center",
            theme_text_color="Secondary"
        )
        
        layout.add_widget(title)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_categories(self, *args):
        """Tester le formulaire catégories"""
        try:
            from screens.categories_screen import CategoriesScreen
            
            # Créer l'écran de test
            categories_screen = CategoriesScreen()
            
            self.result_label.text = "✅ Formulaire catégories chargé avec succès!\\nInterface moderne avec cartes et statistiques activée."
            
            print("✅ Test réussi: Formulaire catégories optimisé fonctionnel")
            
        except Exception as e:
            self.result_label.text = f"❌ Erreur: {str(e)}"
            print(f"❌ Erreur test: {e}")

if __name__ == "__main__":
    QuickTestApp().run()
'''
    
    test_file = project_root / "quick_test_categories.py"
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_widget_content)
        print(f"✅ Widget de test créé: {test_file}")
    except Exception as e:
        print(f"❌ Erreur création widget de test: {e}")
    
    # 3. Créer des fonctionnalités avancées supplémentaires
    print("\n⚡ 3. Création de fonctionnalités avancées...")
    
    # Créer un gestionnaire de catégories avancé
    advanced_manager_content = '''"""
Gestionnaire avancé de catégories avec fonctionnalités étendues
"""

from database.db_manager import DatabaseManager
from datetime import datetime
import json

class AdvancedCategoryManager:
    """Gestionnaire avancé pour les catégories avec fonctionnalités étendues"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_category_statistics(self):
        """Obtenir des statistiques détaillées sur les catégories"""
        try:
            if not self.db_manager.connect():
                return None
            
            # Statistiques générales
            stats = {}
            
            # Total catégories
            total_result = self.db_manager.execute_query("SELECT COUNT(*) as total FROM categories")
            stats['total_categories'] = total_result[0]['total'] if total_result else 0
            
            # Catégories avec produits
            with_products_result = self.db_manager.execute_query("""
                SELECT COUNT(DISTINCT c.id) as count
                FROM categories c
                INNER JOIN produits p ON c.id = p.categorie_id
                WHERE p.actif = 1
            """)
            stats['categories_with_products'] = with_products_result[0]['count'] if with_products_result else 0
            
            # Catégories vides
            stats['empty_categories'] = stats['total_categories'] - stats['categories_with_products']
            
            # Total produits
            total_products_result = self.db_manager.execute_query("""
                SELECT COUNT(*) as total FROM produits WHERE actif = 1
            """)
            stats['total_products'] = total_products_result[0]['total'] if total_products_result else 0
            
            # Produits sans catégorie
            no_category_result = self.db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id IS NULL AND actif = 1
            """)
            stats['products_without_category'] = no_category_result[0]['count'] if no_category_result else 0
            
            # Top 5 catégories par nombre de produits
            top_categories_result = self.db_manager.execute_query("""
                SELECT c.nom, COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom
                ORDER BY products_count DESC
                LIMIT 5
            """)
            stats['top_categories'] = top_categories_result if top_categories_result else []
            
            return stats
            
        except Exception as e:
            print(f"❌ Erreur statistiques catégories: {e}")
            return None
        
        finally:
            self.db_manager.disconnect()
    
    def export_categories_data(self, format='json'):
        """Exporter les données des catégories"""
        try:
            if not self.db_manager.connect():
                return None
            
            # Récupérer toutes les catégories avec leurs produits
            categories_data = self.db_manager.execute_query("""
                SELECT c.*, 
                       COUNT(p.id) as products_count,
                       GROUP_CONCAT(p.nom) as products_list
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom, c.description, c.date_creation
                ORDER BY c.nom ASC
            """)
            
            if not categories_data:
                return None
            
            # Préparer les données pour l'export
            export_data = {
                'export_date': datetime.now().isoformat(),
                'total_categories': len(categories_data),
                'categories': []
            }
            
            for category in categories_data:
                cat_data = {
                    'id': category['id'],
                    'nom': category['nom'],
                    'description': category.get('description', ''),
                    'date_creation': category.get('date_creation', ''),
                    'products_count': category.get('products_count', 0),
                    'products_list': category.get('products_list', '').split(',') if category.get('products_list') else []
                }
                export_data['categories'].append(cat_data)
            
            if format == 'json':
                return json.dumps(export_data, indent=2, ensure_ascii=False)
            else:
                return export_data
            
        except Exception as e:
            print(f"❌ Erreur export catégories: {e}")
            return None
        
        finally:
            self.db_manager.disconnect()
    
    def import_categories_data(self, data):
        """Importer des données de catégories"""
        try:
            if not self.db_manager.connect():
                return False
            
            if isinstance(data, str):
                data = json.loads(data)
            
            imported_count = 0
            
            for category in data.get('categories', []):
                # Vérifier si la catégorie existe déjà
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (category['nom'],)
                )
                
                if not existing:
                    # Créer la catégorie
                    cursor = self.db_manager.connection.cursor()
                    cursor.execute(
                        "INSERT INTO categories (nom, description) VALUES (?, ?)",
                        (category['nom'], category.get('description', ''))
                    )
                    self.db_manager.connection.commit()
                    imported_count += 1
            
            return imported_count
            
        except Exception as e:
            print(f"❌ Erreur import catégories: {e}")
            return False
        
        finally:
            self.db_manager.disconnect()
    
    def optimize_categories(self):
        """Optimiser les catégories (supprimer les vides, réorganiser)"""
        try:
            if not self.db_manager.connect():
                return False
            
            optimizations = []
            
            # 1. Identifier les catégories vides
            empty_categories = self.db_manager.execute_query("""
                SELECT c.id, c.nom
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom
                HAVING COUNT(p.id) = 0
            """)
            
            if empty_categories:
                optimizations.append(f"Trouvé {len(empty_categories)} catégorie(s) vide(s)")
            
            # 2. Identifier les doublons potentiels
            potential_duplicates = self.db_manager.execute_query("""
                SELECT nom, COUNT(*) as count
                FROM categories
                GROUP BY LOWER(nom)
                HAVING count > 1
            """)
            
            if potential_duplicates:
                optimizations.append(f"Trouvé {len(potential_duplicates)} doublon(s) potentiel(s)")
            
            return optimizations
            
        except Exception as e:
            print(f"❌ Erreur optimisation catégories: {e}")
            return []
        
        finally:
            self.db_manager.disconnect()
    
    def get_category_hierarchy(self):
        """Obtenir la hiérarchie des catégories (pour future extension)"""
        # Cette méthode peut être étendue pour supporter des sous-catégories
        try:
            if not self.db_manager.connect():
                return None
            
            categories = self.db_manager.execute_query("""
                SELECT c.*, COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom, c.description, c.date_creation
                ORDER BY c.nom ASC
            """)
            
            # Organiser en hiérarchie (pour l'instant, liste plate)
            hierarchy = {
                'root': {
                    'name': 'Toutes les catégories',
                    'children': []
                }
            }
            
            for category in categories or []:
                hierarchy['root']['children'].append({
                    'id': category['id'],
                    'name': category['nom'],
                    'description': category.get('description', ''),
                    'products_count': category.get('products_count', 0),
                    'children': []  # Pour future extension
                })
            
            return hierarchy
            
        except Exception as e:
            print(f"❌ Erreur hiérarchie catégories: {e}")
            return None
        
        finally:
            self.db_manager.disconnect()
'''
    
    advanced_file = project_root / "utils" / "advanced_category_manager.py"
    
    try:
        # Créer le répertoire utils s'il n'existe pas
        utils_dir = project_root / "utils"
        utils_dir.mkdir(exist_ok=True)
        
        with open(advanced_file, 'w', encoding='utf-8') as f:
            f.write(advanced_manager_content)
        print(f"✅ Gestionnaire avancé créé: {advanced_file}")
    except Exception as e:
        print(f"❌ Erreur création gestionnaire avancé: {e}")
    
    # 4. Créer un écran de statistiques avancées
    print("\n📊 4. Création de l'écran de statistiques avancées...")
    
    stats_screen_content = '''"""
Écran de statistiques avancées pour les catégories
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
from utils.advanced_category_manager import AdvancedCategoryManager

class CategoryStatsScreen(MDScreen):
    """Écran de statistiques avancées pour les catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.category_manager = AdvancedCategoryManager()
        self.create_interface()
        self.load_statistics()
    
    def create_interface(self):
        """Créer l'interface des statistiques"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📊 Statistiques Catégories",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[
                ["refresh", lambda x: self.refresh_stats()],
                ["download", lambda x: self.export_data()]
            ],
            elevation=2
        )
        
        # Contenu scrollable
        scroll = MDScrollView()
        
        self.content_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            padding="16dp",
            adaptive_height=True
        )
        
        # Indicateur de chargement
        self.loading_label = MDLabel(
            text="🔄 Chargement des statistiques...",
            font_style="H6",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        self.content_layout.add_widget(self.loading_label)
        
        scroll.add_widget(self.content_layout)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def load_statistics(self):
        """Charger les statistiques de manière asynchrone"""
        def load_data():
            try:
                stats = self.category_manager.get_category_statistics()
                if stats:
                    Clock.schedule_once(lambda dt: self.display_statistics(stats), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_error(), 0)
            except Exception as e:
                print(f"❌ Erreur chargement statistiques: {e}")
                Clock.schedule_once(lambda dt: self.show_error(), 0)
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def display_statistics(self, stats):
        """Afficher les statistiques"""
        self.content_layout.clear_widgets()
        
        # Titre
        title = MDLabel(
            text="📊 Statistiques Détaillées des Catégories",
            font_style="H5",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        self.content_layout.add_widget(title)
        
        # Statistiques générales
        self.create_general_stats_card(stats)
        
        # Top catégories
        self.create_top_categories_card(stats)
        
        # Actions rapides
        self.create_actions_card()
    
    def create_general_stats_card(self, stats):
        """Créer la carte des statistiques générales"""
        card = MDCard(
            MDBoxLayout(
                orientation='vertical',
                padding="16dp",
                spacing="12dp"
            ),
            size_hint_y=None,
            height="200dp",
            elevation=2,
            radius=[8]
        )
        
        card_title = MDLabel(
            text="📈 Statistiques Générales",
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        stats_grid = MDGridLayout(
            cols=2,
            spacing="8dp",
            adaptive_height=True
        )
        
        # Statistiques
        stats_items = [
            ("📂 Total catégories", stats.get('total_categories', 0)),
            ("📦 Avec produits", stats.get('categories_with_products', 0)),
            ("📭 Catégories vides", stats.get('empty_categories', 0)),
            ("🎯 Total produits", stats.get('total_products', 0)),
            ("❓ Produits sans catégorie", stats.get('products_without_category', 0)),
            ("📊 Moyenne produits/catégorie", 
             round(stats.get('total_products', 0) / max(stats.get('total_categories', 1), 1), 1))
        ]
        
        for label_text, value in stats_items:
            label = MDLabel(
                text=label_text,
                font_style="Body2",
                size_hint_y=None,
                height="25dp"
            )
            
            value_label = MDLabel(
                text=str(value),
                font_style="H6",
                theme_text_color="Primary",
                halign="right",
                size_hint_y=None,
                height="25dp"
            )
            
            stats_grid.add_widget(label)
            stats_grid.add_widget(value_label)
        
        card.children[0].add_widget(card_title)
        card.children[0].add_widget(stats_grid)
        
        self.content_layout.add_widget(card)
    
    def create_top_categories_card(self, stats):
        """Créer la carte du top des catégories"""
        card = MDCard(
            MDBoxLayout(
                orientation='vertical',
                padding="16dp",
                spacing="12dp"
            ),
            size_hint_y=None,
            height="250dp",
            elevation=2,
            radius=[8]
        )
        
        card_title = MDLabel(
            text="🏆 Top 5 Catégories",
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        top_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            adaptive_height=True
        )
        
        top_categories = stats.get('top_categories', [])
        
        if top_categories:
            for i, category in enumerate(top_categories, 1):
                # Icône de rang
                if i == 1:
                    icon = "🥇"
                elif i == 2:
                    icon = "🥈"
                elif i == 3:
                    icon = "🥉"
                else:
                    icon = f"{i}."
                
                category_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height="30dp",
                    spacing="8dp"
                )
                
                rank_label = MDLabel(
                    text=icon,
                    size_hint_x=None,
                    width="40dp",
                    halign="center"
                )
                
                name_label = MDLabel(
                    text=category.get('nom', 'Sans nom'),
                    font_style="Body1"
                )
                
                count_label = MDLabel(
                    text=f"{category.get('products_count', 0)} produits",
                    font_style="Caption",
                    theme_text_color="Secondary",
                    halign="right",
                    size_hint_x=None,
                    width="100dp"
                )
                
                category_layout.add_widget(rank_label)
                category_layout.add_widget(name_label)
                category_layout.add_widget(count_label)
                
                top_layout.add_widget(category_layout)
        else:
            no_data_label = MDLabel(
                text="Aucune donnée disponible",
                font_style="Caption",
                theme_text_color="Secondary",
                halign="center"
            )
            top_layout.add_widget(no_data_label)
        
        card.children[0].add_widget(card_title)
        card.children[0].add_widget(top_layout)
        
        self.content_layout.add_widget(card)
    
    def create_actions_card(self):
        """Créer la carte des actions rapides"""
        card = MDCard(
            MDBoxLayout(
                orientation='vertical',
                padding="16dp",
                spacing="12dp"
            ),
            size_hint_y=None,
            height="150dp",
            elevation=2,
            radius=[8]
        )
        
        card_title = MDLabel(
            text="⚡ Actions Rapides",
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        actions_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="8dp",
            size_hint_y=None,
            height="48dp"
        )
        
        export_btn = MDRaisedButton(
            text="📤 Exporter",
            on_release=self.export_data
        )
        
        optimize_btn = MDRaisedButton(
            text="⚡ Optimiser",
            on_release=self.optimize_categories
        )
        
        refresh_btn = MDRaisedButton(
            text="🔄 Actualiser",
            on_release=self.refresh_stats
        )
        
        actions_layout.add_widget(export_btn)
        actions_layout.add_widget(optimize_btn)
        actions_layout.add_widget(refresh_btn)
        
        card.children[0].add_widget(card_title)
        card.children[0].add_widget(actions_layout)
        
        self.content_layout.add_widget(card)
    
    def show_error(self):
        """Afficher un message d'erreur"""
        self.content_layout.clear_widgets()
        
        error_label = MDLabel(
            text="❌ Erreur lors du chargement des statistiques",
            font_style="H6",
            halign="center",
            theme_text_color="Error"
        )
        
        retry_btn = MDRaisedButton(
            text="🔄 Réessayer",
            size_hint=(None, None),
            size=("200dp", "48dp"),
            pos_hint={"center_x": 0.5},
            on_release=lambda x: self.load_statistics()
        )
        
        self.content_layout.add_widget(error_label)
        self.content_layout.add_widget(retry_btn)
    
    def refresh_stats(self, *args):
        """Actualiser les statistiques"""
        self.content_layout.clear_widgets()
        self.content_layout.add_widget(self.loading_label)
        self.load_statistics()
    
    def export_data(self, *args):
        """Exporter les données"""
        def export():
            try:
                data = self.category_manager.export_categories_data()
                if data:
                    # Sauvegarder dans un fichier
                    from datetime import datetime
                    filename = f"categories_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(data)
                    
                    Clock.schedule_once(lambda dt: print(f"✅ Export réussi: {filename}"), 0)
                else:
                    Clock.schedule_once(lambda dt: print("❌ Erreur lors de l'export"), 0)
            except Exception as e:
                Clock.schedule_once(lambda dt: print(f"❌ Erreur export: {e}"), 0)
        
        threading.Thread(target=export, daemon=True).start()
    
    def optimize_categories(self, *args):
        """Optimiser les catégories"""
        def optimize():
            try:
                optimizations = self.category_manager.optimize_categories()
                message = "\\n".join(optimizations) if optimizations else "Aucune optimisation nécessaire"
                Clock.schedule_once(lambda dt: print(f"⚡ Optimisations: {message}"), 0)
            except Exception as e:
                Clock.schedule_once(lambda dt: print(f"❌ Erreur optimisation: {e}"), 0)
        
        threading.Thread(target=optimize, daemon=True).start()
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'categories'
'''
    
    stats_file = project_root / "screens" / "category_stats_screen.py"
    
    try:
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write(stats_screen_content)
        print(f"✅ Écran de statistiques créé: {stats_file}")
    except Exception as e:
        print(f"❌ Erreur création écran statistiques: {e}")
    
    print("\n🎉 INTÉGRATION AUTOMATIQUE TERMINÉE")
    print("=" * 50)
    print("✅ Formulaire catégories optimisé intégré")
    print("✅ Widget de test rapide créé")
    print("✅ Gestionnaire avancé créé")
    print("✅ Écran de statistiques créé")
    
    print("\n🚀 Prochaines étapes:")
    print("1. Tester: python quick_test_categories.py")
    print("2. Lancer l'app: python launch_optimized.py")
    print("3. Utiliser le bouton '📂 Catégories' dans l'interface")
    
    return True

if __name__ == "__main__":
    auto_integrate()