#!/usr/bin/env python3
"""
Test de la liaison entre catégories et produits
"""

import os
import sys
import time

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager, get_all_categories, get_all_products, add_product, update_product, add_category

def test_liaison_categories_produits():
    """Tester la liaison complète entre catégories et produits"""
    print("🔗 TEST DE LIAISON CATÉGORIES ↔ PRODUITS")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    if not db_manager.connect():
        print("❌ Impossible de se connecter à la base de données")
        return
    
    if not db_manager.initialize_database():
        print("❌ Impossible d'initialiser la base de données")
        return
    
    print("✅ Base de données initialisée")
    
    try:
        # ==================== 1. VÉRIFIER LES CATÉGORIES EXISTANTES ====================
        print("\n📂 1. VÉRIFICATION DES CATÉGORIES EXISTANTES")
        print("-" * 40)
        
        categories = get_all_categories(db_manager)
        print(f"✅ {len(categories)} catégories trouvées")
        
        if not categories:
            print("⚠️ Aucune catégorie trouvée, création d'une catégorie de test...")
            test_category = {
                'nom': f'Test Liaison {int(time.time())}',
                'description': 'Catégorie créée pour tester la liaison avec les produits'
            }
            category_id = add_category(db_manager, test_category)
            print(f"✅ Catégorie de test créée (ID: {category_id})")
            categories = get_all_categories(db_manager)
        
        # Afficher les catégories disponibles
        print("\n📋 Catégories disponibles pour liaison :")
        for cat in categories[:5]:  # Afficher max 5
            # Compter les produits dans cette catégorie
            products_count = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
            """, (cat['id'],))
            count = products_count[0]['count'] if products_count else 0
            
            print(f"   📂 ID:{cat['id']:2d} | {cat['nom']:<20} | {count} produits")
        
        # ==================== 2. CRÉER UN PRODUIT AVEC CATÉGORIE ====================
        print("\n📦 2. CRÉATION D'UN PRODUIT AVEC CATÉGORIE")
        print("-" * 40)
        
        # Prendre la première catégorie disponible
        test_category = categories[0]
        
        # Créer un produit de test avec catégorie
        test_product = {
            'nom': f'Produit Test Liaison {int(time.time())}',
            'description': 'Produit créé pour tester la liaison avec les catégories',
            'reference': f'TEST-LIAISON-{int(time.time())}',
            'categorie_id': test_category['id'],  # LIAISON ICI
            'prix_achat': 100.0,
            'prix_vente': 150.0,
            'stock_actuel': 10,
            'stock_minimum': 2,
            'tva': 20.0,
            'actif': True
        }
        
        product_id = add_product(db_manager, test_product)
        print(f"✅ Produit créé avec liaison (ID: {product_id})")
        print(f"   📦 Produit: {test_product['nom']}")
        print(f"   📂 Catégorie: {test_category['nom']} (ID: {test_category['id']})")
        
        # ==================== 3. VÉRIFIER LA LIAISON ====================
        print("\n🔗 3. VÉRIFICATION DE LA LIAISON")
        print("-" * 40)
        
        # Récupérer le produit avec sa catégorie
        product_with_category = db_manager.execute_query("""
            SELECT p.*, c.nom as categorie_nom, c.description as categorie_description
            FROM produits p
            LEFT JOIN categories c ON p.categorie_id = c.id
            WHERE p.id = ?
        """, (product_id,))
        
        if product_with_category:
            product = product_with_category[0]
            print("✅ Liaison vérifiée avec succès !")
            print(f"   📦 Produit: {product['nom']}")
            print(f"   📂 Catégorie liée: {product.get('categorie_nom', 'Aucune')}")
            print(f"   🔗 ID Catégorie: {product.get('categorie_id', 'NULL')}")
            
            if product.get('categorie_nom'):
                print(f"   📝 Description catégorie: {product.get('categorie_description', 'Pas de description')}")
            else:
                print("   ⚠️ Aucune catégorie liée")
        else:
            print("❌ Impossible de récupérer le produit créé")
        
        # ==================== 4. TESTER LA MODIFICATION DE CATÉGORIE ====================
        print("\n✏️ 4. TEST DE MODIFICATION DE CATÉGORIE")
        print("-" * 40)
        
        # Prendre une autre catégorie si disponible
        if len(categories) > 1:
            new_category = categories[1]
            
            # Modifier le produit pour changer sa catégorie
            update_data = {
                'id': product_id,
                'nom': test_product['nom'],
                'description': test_product['description'],
                'reference': test_product['reference'],
                'categorie_id': new_category['id'],  # NOUVELLE LIAISON
                'prix_achat': test_product['prix_achat'],
                'prix_vente': test_product['prix_vente'],
                'stock_actuel': test_product['stock_actuel'],
                'stock_minimum': test_product['stock_minimum'],
                'tva': test_product['tva'],
                'actif': test_product['actif']
            }
            
            success = update_product(db_manager, update_data)
            
            if success:
                print(f"✅ Catégorie modifiée avec succès !")
                print(f"   📂 Ancienne catégorie: {test_category['nom']}")
                print(f"   📂 Nouvelle catégorie: {new_category['nom']}")
                
                # Vérifier la modification
                updated_product = db_manager.execute_query("""
                    SELECT p.*, c.nom as categorie_nom
                    FROM produits p
                    LEFT JOIN categories c ON p.categorie_id = c.id
                    WHERE p.id = ?
                """, (product_id,))
                
                if updated_product:
                    product = updated_product[0]
                    print(f"   🔗 Catégorie actuelle: {product.get('categorie_nom', 'Aucune')}")
                else:
                    print("❌ Impossible de vérifier la modification")
            else:
                print("❌ Échec de la modification de catégorie")
        else:
            print("⚠️ Une seule catégorie disponible, impossible de tester la modification")
        
        # ==================== 5. TESTER LA SUPPRESSION DE LIAISON ====================
        print("\n🗑️ 5. TEST DE SUPPRESSION DE LIAISON")
        print("-" * 40)
        
        # Mettre le produit sans catégorie (categorie_id = NULL)
        update_data_no_cat = {
            'id': product_id,
            'nom': test_product['nom'],
            'description': test_product['description'],
            'reference': test_product['reference'],
            'categorie_id': None,  # SUPPRESSION DE LA LIAISON
            'prix_achat': test_product['prix_achat'],
            'prix_vente': test_product['prix_vente'],
            'stock_actuel': test_product['stock_actuel'],
            'stock_minimum': test_product['stock_minimum'],
            'tva': test_product['tva'],
            'actif': test_product['actif']
        }
        
        success = update_product(db_manager, update_data_no_cat)
        
        if success:
            print("✅ Liaison supprimée avec succès !")
            
            # Vérifier la suppression
            product_no_cat = db_manager.execute_query("""
                SELECT p.*, c.nom as categorie_nom
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                WHERE p.id = ?
            """, (product_id,))
            
            if product_no_cat:
                product = product_no_cat[0]
                print(f"   📦 Produit: {product['nom']}")
                print(f"   📂 Catégorie: {product.get('categorie_nom') or 'Aucune (NULL)'}")
                print(f"   🔗 ID Catégorie: {product.get('categorie_id') or 'NULL'}")
            else:
                print("❌ Impossible de vérifier la suppression de liaison")
        else:
            print("❌ Échec de la suppression de liaison")
        
        # ==================== 6. STATISTIQUES DE LIAISON ====================
        print("\n📊 6. STATISTIQUES DE LIAISON")
        print("-" * 40)
        
        # Compter les produits par catégorie
        stats = db_manager.execute_query("""
            SELECT 
                c.nom as categorie_nom,
                c.id as categorie_id,
                COUNT(p.id) as nb_produits
            FROM categories c
            LEFT JOIN produits p ON c.id = p.categorie_id
            GROUP BY c.id, c.nom
            ORDER BY nb_produits DESC, c.nom
        """)
        
        print("📋 Répartition des produits par catégorie :")
        total_produits_lies = 0
        for stat in stats:
            nb = stat['nb_produits']
            total_produits_lies += nb
            print(f"   📂 {stat['categorie_nom']:<25} : {nb:2d} produits")
        
        # Compter les produits sans catégorie
        produits_sans_cat = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM produits WHERE categorie_id IS NULL
        """)
        nb_sans_cat = produits_sans_cat[0]['count'] if produits_sans_cat else 0
        
        print(f"\n📊 Résumé des liaisons :")
        print(f"   📂 Produits avec catégorie : {total_produits_lies}")
        print(f"   📭 Produits sans catégorie : {nb_sans_cat}")
        print(f"   📦 Total produits         : {total_produits_lies + nb_sans_cat}")
        
        # ==================== 7. TEST DE L'INTERFACE DROPDOWN ====================
        print("\n🎨 7. TEST DE L'INTERFACE DROPDOWN")
        print("-" * 40)
        
        print("✅ Simulation du comportement de la liste déroulante :")
        print("   1. Chargement des catégories...")
        categories_for_dropdown = get_all_categories(db_manager)
        print(f"      📂 {len(categories_for_dropdown)} catégories chargées")
        
        print("   2. Création des éléments de menu...")
        menu_items = [
            {"text": "Aucune catégorie", "id": None}
        ]
        for cat in categories_for_dropdown:
            menu_items.append({
                "text": f"📂 {cat['nom']}",
                "id": cat['id']
            })
        print(f"      🎯 {len(menu_items)} éléments de menu créés")
        
        print("   3. Affichage des options disponibles :")
        for i, item in enumerate(menu_items):
            print(f"      {i+1}. {item['text']} (ID: {item['id']})")
        
        print("\n🎉 TESTS DE LIAISON TERMINÉS AVEC SUCCÈS !")
        print("=" * 50)
        print("✅ Fonctionnalités de liaison validées :")
        print("   • 🔗 CRÉATION avec catégorie")
        print("   • 📖 LECTURE avec jointure")
        print("   • ✏️ MODIFICATION de catégorie")
        print("   • 🗑️ SUPPRESSION de liaison")
        print("   • 📊 STATISTIQUES de répartition")
        print("   • 🎨 INTERFACE dropdown simulée")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests de liaison: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if db_manager.connection:
            db_manager.disconnect()
            print("🔒 Connexion fermée proprement")

if __name__ == "__main__":
    test_liaison_categories_produits()