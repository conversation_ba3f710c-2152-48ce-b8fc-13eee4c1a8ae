"""
Nouveau formulaire de catégorie basé sur la structure exacte de la table categories
"""

from kivymd.uix.dialog import MDDialog
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import <PERSON><PERSON>abe<PERSON>
from kivymd.uix.textfield import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from kivymd.uix.button import MDR<PERSON>edButton, MDFlatButton
from kivymd.uix.snackbar import MDSnackbar
from database.db_manager import DatabaseManager


class CategoryFormDialog(MDDialog):
    """
    Formulaire de catégorie basé sur la structure exacte de la table categories
    
    Structure de la table categories:
    - id INTEGER PRIMARY KEY AUTOINCREMENT
    - nom TEXT NOT NULL UNIQUE  
    - description TEXT
    - date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    """
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Créer les boutons
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        # Déterminer le mode (création ou modification)
        self.is_edit_mode = bool(self.category_data and self.category_data.get('id'))
        
        super().__init__(
            title="✏️ Modifier la catégorie" if self.is_edit_mode else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.85, None),
            height="500dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
        
        self.build_form()
    
    def build_form(self):
        """Construire le formulaire basé sur la structure de la table"""
        # Container principal
        main_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            padding="20dp",
            size_hint_y=None,
            height="400dp"
        )
        
        # En-tête du formulaire
        header = self.create_header()
        main_layout.add_widget(header)
        
        # Champs basés sur la table categories
        fields_section = self.create_fields_section()
        main_layout.add_widget(fields_section)
        
        # Section informations (si modification)
        if self.is_edit_mode:
            info_section = self.create_info_section()
            main_layout.add_widget(info_section)
        
        # Assigner le contenu
        self.content_cls = main_layout
    
    def create_header(self):
        """Créer l'en-tête du formulaire"""
        header_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="60dp"
        )
        
        # Titre principal
        title = MDLabel(
            text="📂 Formulaire Catégorie",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="30dp",
            halign="center"
        )
        
        # Sous-titre avec mode
        mode_text = "Modification d'une catégorie existante" if self.is_edit_mode else "Création d'une nouvelle catégorie"
        subtitle = MDLabel(
            text=mode_text,
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="20dp",
            halign="center"
        )
        
        header_layout.add_widget(title)
        header_layout.add_widget(subtitle)
        
        return header_layout
    
    def create_fields_section(self):
        """Créer la section des champs basée sur la table categories"""
        fields_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Champ 1: nom (NOT NULL UNIQUE)
        nom_section = self.create_nom_field()
        fields_layout.add_widget(nom_section)
        
        # Champ 2: description (TEXT, optionnel)
        description_section = self.create_description_field()
        fields_layout.add_widget(description_section)
        
        return fields_layout
    
    def create_nom_field(self):
        """Créer le champ nom (colonne nom de la table)"""
        nom_layout = MDBoxLayout(
            orientation='vertical',
            spacing="4dp",
            size_hint_y=None,
            height="80dp"
        )
        
        # Label avec contraintes de la table
        nom_label = MDLabel(
            text="📝 Nom de la catégorie (OBLIGATOIRE - UNIQUE)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        # Champ nom avec validation
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Nom unique de la catégorie (ex: Électronique, Vêtements...)",
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            required=True,
            # Couleurs professionnelles mais visibles
            line_color_normal=[0.4, 0.4, 0.4, 1],
            line_color_focus=[0.1, 0.6, 0.9, 1],
            text_color_normal=[0.1, 0.1, 0.1, 1],
            hint_text_color_normal=[0.6, 0.6, 0.6, 1],
            # Validation en temps réel
            on_text_validate=self.validate_nom,
            on_focus=self.on_nom_focus
        )
        
        nom_layout.add_widget(nom_label)
        nom_layout.add_widget(self.nom_field)
        
        return nom_layout
    
    def create_description_field(self):
        """Créer le champ description (colonne description de la table)"""
        desc_layout = MDBoxLayout(
            orientation='vertical',
            spacing="4dp",
            size_hint_y=None,
            height="120dp"
        )
        
        # Label
        desc_label = MDLabel(
            text="📄 Description (OPTIONNELLE)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        # Champ description multiline
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description détaillée de la catégorie (optionnelle)",
            multiline=True,
            size_hint_y=None,
            height="96dp",
            mode="rectangle",
            # Couleurs cohérentes
            line_color_normal=[0.4, 0.4, 0.4, 1],
            line_color_focus=[0.1, 0.6, 0.9, 1],
            text_color_normal=[0.1, 0.1, 0.1, 1],
            hint_text_color_normal=[0.6, 0.6, 0.6, 1],
            max_text_length=500
        )
        
        desc_layout.add_widget(desc_label)
        desc_layout.add_widget(self.description_field)
        
        return desc_layout
    
    def create_info_section(self):
        """Créer la section d'informations (pour modification uniquement)"""
        info_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        # Titre de la section
        info_title = MDLabel(
            text="ℹ️ Informations de la base de données",
            font_style="Subtitle2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="24dp"
        )
        
        # Informations en ligne
        info_row = MDBoxLayout(
            orientation='horizontal',
            spacing="16dp",
            size_hint_y=None,
            height="48dp"
        )
        
        # ID (PRIMARY KEY AUTOINCREMENT)
        id_info = MDLabel(
            text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.3
        )
        
        # Date de création (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
        date_creation = self.category_data.get('date_creation', '')
        if date_creation:
            try:
                from datetime import datetime
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y %H:%M')
                else:
                    date_formatted = str(date_creation)[:16]
            except:
                date_formatted = str(date_creation)[:16]
        else:
            date_formatted = "Non définie"
        
        date_info = MDLabel(
            text=f"📅 Créée: {date_formatted}",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.4
        )
        
        # Nombre de produits liés (relation avec table produits)
        products_count = self.category_data.get('products_count', 0)
        products_info = MDLabel(
            text=f"📦 {products_count} produit(s)",
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_x=0.3
        )
        
        info_row.add_widget(id_info)
        info_row.add_widget(date_info)
        info_row.add_widget(products_info)
        
        info_layout.add_widget(info_title)
        info_layout.add_widget(info_row)
        
        return info_layout
    
    def validate_nom(self, *args):
        """Valider le nom en temps réel"""
        nom = self.nom_field.text.strip()
        
        if not nom:
            self.nom_field.error = True
            self.nom_field.helper_text = "Le nom est obligatoire"
            return False
        
        if len(nom) < 2:
            self.nom_field.error = True
            self.nom_field.helper_text = "Le nom doit contenir au moins 2 caractères"
            return False
        
        # Vérifier l'unicité (sauf pour la catégorie actuelle en modification)
        if self.check_nom_exists(nom):
            self.nom_field.error = True
            self.nom_field.helper_text = "Ce nom existe déjà"
            return False
        
        self.nom_field.error = False
        self.nom_field.helper_text = ""
        return True
    
    def on_nom_focus(self, instance, focus):
        """Gérer le focus du champ nom"""
        if not focus:  # Quand on perd le focus
            self.validate_nom()
    
    def check_nom_exists(self, nom):
        """Vérifier si le nom existe déjà dans la table categories"""
        try:
            if not self.db_manager.connect():
                return False
            
            if self.is_edit_mode:
                # En modification, exclure l'ID actuel
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ? AND id != ?",
                    (nom, self.category_data['id'])
                )
            else:
                # En création, vérifier tous les noms
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (nom,)
                )
            
            return bool(existing)
            
        except Exception as e:
            print(f"Erreur lors de la vérification du nom: {e}")
            return False
        finally:
            self.db_manager.close()
    
    def save_category(self, *args):
        """Sauvegarder dans la table categories"""
        # Validation finale
        if not self.validate_nom():
            self.show_error("Veuillez corriger les erreurs dans le formulaire")
            return
        
        nom = self.nom_field.text.strip()
        description = self.description_field.text.strip() or None  # NULL si vide
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.is_edit_mode:
                # UPDATE sur la table categories
                success = self.db_manager.execute_update(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id'])
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    result_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description,
                        'date_creation': self.category_data.get('date_creation')
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            else:
                # INSERT dans la table categories
                category_id = self.db_manager.execute_insert(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    result_data = {
                        'id': category_id,
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            # Callback avec les données
            if self.on_save_callback:
                self.on_save_callback(result_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur base de données: {str(e)}")
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception:
            print(f"✅ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()