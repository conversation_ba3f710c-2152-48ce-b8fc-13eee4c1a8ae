"""
Écran de statistiques avancées pour les catégories
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MD<PERSON>ard
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
from utils.advanced_category_manager import AdvancedCategoryManager

class CategoryStatsScreen(MDScreen):
    """Écran de statistiques avancées pour les catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.category_manager = AdvancedCategoryManager()
        self.create_interface()
        self.load_statistics()
    
    def create_interface(self):
        """Créer l'interface des statistiques"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📊 Statistiques Catégories",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[
                ["refresh", lambda x: self.refresh_stats()],
                ["download", lambda x: self.export_data()]
            ],
            elevation=2
        )
        
        # Contenu scrollable
        scroll = MDScrollView()
        
        self.content_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            padding="16dp",
            adaptive_height=True
        )
        
        # Indicateur de chargement
        self.loading_label = MDLabel(
            text="🔄 Chargement des statistiques...",
            font_style="H6",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        self.content_layout.add_widget(self.loading_label)
        
        scroll.add_widget(self.content_layout)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def load_statistics(self):
        """Charger les statistiques de manière asynchrone"""
        def load_data():
            try:
                stats = self.category_manager.get_category_statistics()
                if stats:
                    Clock.schedule_once(lambda dt: self.display_statistics(stats), 0)
                else:
                    Clock.schedule_once(lambda dt: self.show_error(), 0)
            except Exception as e:
                print(f"❌ Erreur chargement statistiques: {e}")
                Clock.schedule_once(lambda dt: self.show_error(), 0)
        
        threading.Thread(target=load_data, daemon=True).start()
    
    def display_statistics(self, stats):
        """Afficher les statistiques"""
        self.content_layout.clear_widgets()
        
        # Titre
        title = MDLabel(
            text="📊 Statistiques Détaillées des Catégories",
            font_style="H5",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        self.content_layout.add_widget(title)
        
        # Statistiques générales
        self.create_general_stats_card(stats)
        
        # Top catégories
        self.create_top_categories_card(stats)
        
        # Actions rapides
        self.create_actions_card()
    
    def create_general_stats_card(self, stats):
        """Créer la carte des statistiques générales"""
        card = MDCard(
            MDBoxLayout(
                orientation='vertical',
                padding="16dp",
                spacing="12dp"
            ),
            size_hint_y=None,
            height="200dp",
            elevation=2,
            radius=[8]
        )
        
        card_title = MDLabel(
            text="📈 Statistiques Générales",
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        stats_grid = MDGridLayout(
            cols=2,
            spacing="8dp",
            adaptive_height=True
        )
        
        # Statistiques
        stats_items = [
            ("📂 Total catégories", stats.get('total_categories', 0)),
            ("📦 Avec produits", stats.get('categories_with_products', 0)),
            ("📭 Catégories vides", stats.get('empty_categories', 0)),
            ("🎯 Total produits", stats.get('total_products', 0)),
            ("❓ Produits sans catégorie", stats.get('products_without_category', 0)),
            ("📊 Moyenne produits/catégorie", 
             round(stats.get('total_products', 0) / max(stats.get('total_categories', 1), 1), 1))
        ]
        
        for label_text, value in stats_items:
            label = MDLabel(
                text=label_text,
                font_style="Body2",
                size_hint_y=None,
                height="25dp"
            )
            
            value_label = MDLabel(
                text=str(value),
                font_style="H6",
                theme_text_color="Primary",
                halign="right",
                size_hint_y=None,
                height="25dp"
            )
            
            stats_grid.add_widget(label)
            stats_grid.add_widget(value_label)
        
        card.children[0].add_widget(card_title)
        card.children[0].add_widget(stats_grid)
        
        self.content_layout.add_widget(card)
    
    def create_top_categories_card(self, stats):
        """Créer la carte du top des catégories"""
        card = MDCard(
            MDBoxLayout(
                orientation='vertical',
                padding="16dp",
                spacing="12dp"
            ),
            size_hint_y=None,
            height="250dp",
            elevation=2,
            radius=[8]
        )
        
        card_title = MDLabel(
            text="🏆 Top 5 Catégories",
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        top_layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            adaptive_height=True
        )
        
        top_categories = stats.get('top_categories', [])
        
        if top_categories:
            for i, category in enumerate(top_categories, 1):
                # Icône de rang
                if i == 1:
                    icon = "🥇"
                elif i == 2:
                    icon = "🥈"
                elif i == 3:
                    icon = "🥉"
                else:
                    icon = f"{i}."
                
                category_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height="30dp",
                    spacing="8dp"
                )
                
                rank_label = MDLabel(
                    text=icon,
                    size_hint_x=None,
                    width="40dp",
                    halign="center"
                )
                
                name_label = MDLabel(
                    text=category.get('nom', 'Sans nom'),
                    font_style="Body1"
                )
                
                count_label = MDLabel(
                    text=f"{category.get('products_count', 0)} produits",
                    font_style="Caption",
                    theme_text_color="Secondary",
                    halign="right",
                    size_hint_x=None,
                    width="100dp"
                )
                
                category_layout.add_widget(rank_label)
                category_layout.add_widget(name_label)
                category_layout.add_widget(count_label)
                
                top_layout.add_widget(category_layout)
        else:
            no_data_label = MDLabel(
                text="Aucune donnée disponible",
                font_style="Caption",
                theme_text_color="Secondary",
                halign="center"
            )
            top_layout.add_widget(no_data_label)
        
        card.children[0].add_widget(card_title)
        card.children[0].add_widget(top_layout)
        
        self.content_layout.add_widget(card)
    
    def create_actions_card(self):
        """Créer la carte des actions rapides"""
        card = MDCard(
            MDBoxLayout(
                orientation='vertical',
                padding="16dp",
                spacing="12dp"
            ),
            size_hint_y=None,
            height="150dp",
            elevation=2,
            radius=[8]
        )
        
        card_title = MDLabel(
            text="⚡ Actions Rapides",
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        actions_layout = MDBoxLayout(
            orientation='horizontal',
            spacing="8dp",
            size_hint_y=None,
            height="48dp"
        )
        
        export_btn = MDRaisedButton(
            text="📤 Exporter",
            on_release=self.export_data
        )
        
        optimize_btn = MDRaisedButton(
            text="⚡ Optimiser",
            on_release=self.optimize_categories
        )
        
        refresh_btn = MDRaisedButton(
            text="🔄 Actualiser",
            on_release=self.refresh_stats
        )
        
        actions_layout.add_widget(export_btn)
        actions_layout.add_widget(optimize_btn)
        actions_layout.add_widget(refresh_btn)
        
        card.children[0].add_widget(card_title)
        card.children[0].add_widget(actions_layout)
        
        self.content_layout.add_widget(card)
    
    def show_error(self):
        """Afficher un message d'erreur"""
        self.content_layout.clear_widgets()
        
        error_label = MDLabel(
            text="❌ Erreur lors du chargement des statistiques",
            font_style="H6",
            halign="center",
            theme_text_color="Error"
        )
        
        retry_btn = MDRaisedButton(
            text="🔄 Réessayer",
            size_hint=(None, None),
            size=("200dp", "48dp"),
            pos_hint={"center_x": 0.5},
            on_release=lambda x: self.load_statistics()
        )
        
        self.content_layout.add_widget(error_label)
        self.content_layout.add_widget(retry_btn)
    
    def refresh_stats(self, *args):
        """Actualiser les statistiques"""
        self.content_layout.clear_widgets()
        self.content_layout.add_widget(self.loading_label)
        self.load_statistics()
    
    def export_data(self, *args):
        """Exporter les données"""
        def export():
            try:
                data = self.category_manager.export_categories_data()
                if data:
                    # Sauvegarder dans un fichier
                    from datetime import datetime
                    filename = f"categories_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(data)
                    
                    Clock.schedule_once(lambda dt: print(f"✅ Export réussi: {filename}"), 0)
                else:
                    Clock.schedule_once(lambda dt: print("❌ Erreur lors de l'export"), 0)
            except Exception as e:
                Clock.schedule_once(lambda dt: print(f"❌ Erreur export: {e}"), 0)
        
        threading.Thread(target=export, daemon=True).start()
    
    def optimize_categories(self, *args):
        """Optimiser les catégories"""
        def optimize():
            try:
                optimizations = self.category_manager.optimize_categories()
                message = "\n".join(optimizations) if optimizations else "Aucune optimisation nécessaire"
                Clock.schedule_once(lambda dt: print(f"⚡ Optimisations: {message}"), 0)
            except Exception as e:
                Clock.schedule_once(lambda dt: print(f"❌ Erreur optimisation: {e}"), 0)
        
        threading.Thread(target=optimize, daemon=True).start()
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'categories'
