"""
Écran de gestion des produits - Version corrigée
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.app import MDApp
from kivy.clock import Clock
import threading
from utils.helpers import format_currency


class ProductCard(MDCard):
    """Carte pour afficher un produit"""
    
    def __init__(self, product_data, on_edit_callback, on_delete_callback, **kwargs):
        super().__init__(**kwargs)
        self.product_data = product_data
        self.elevation = 2
        self.padding = "16dp"
        self.size_hint_y = None
        self.height = "160dp"
        self.spacing = "8dp"
        
        layout = MDBoxLayout(orientation='vertical', spacing="4dp")
        
        # En-tête avec nom et actions
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="32dp")
        
        nom_label = MDLabel(
            text=product_data.get('nom', 'Produit sans nom'),
            font_style="Subtitle1",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        # Boutons d'action
        actions_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.3, spacing="4dp")
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Primary",
            on_release=lambda x: on_edit_callback(product_data)
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Error",
            on_release=lambda x: on_delete_callback(product_data)
        )
        
        actions_layout.add_widget(edit_btn)
        actions_layout.add_widget(delete_btn)
        
        header_layout.add_widget(nom_label)
        header_layout.add_widget(actions_layout)
        
        # Informations du produit
        info_layout = MDBoxLayout(orientation='vertical', spacing="2dp")
        
        # Référence et catégorie
        ref_label = MDLabel(
            text=f"Réf: {product_data.get('reference', 'N/A')}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="16dp"
        )
        
        # Catégorie
        categorie_nom = product_data.get('categorie_nom')
        if categorie_nom:
            categorie_label = MDLabel(
                text=f"📂 {categorie_nom}",
                font_style="Caption",
                theme_text_color="Primary",
                size_hint_y=None,
                height="16dp"
            )
            info_layout.add_widget(categorie_label)
        else:
            categorie_label = MDLabel(
                text="📭 Aucune catégorie",
                font_style="Caption",
                theme_text_color="Hint",
                size_hint_y=None,
                height="16dp"
            )
            info_layout.add_widget(categorie_label)
        
        if product_data.get('code_barre'):
            barcode_label = MDLabel(
                text=f"Code-barres: {product_data.get('code_barre')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="16dp"
            )
            info_layout.add_widget(barcode_label)
        
        # Prix
        prix_vente = product_data.get('prix_vente', 0)
        prix_label = MDLabel(
            text=f"Prix: {format_currency(prix_vente)}",
            font_style="Body1",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        # Stock
        stock_actuel = product_data.get('stock_actuel', 0)
        stock_minimum = product_data.get('stock_minimum', 0)
        
        if stock_actuel <= stock_minimum and stock_minimum > 0:
            stock_color = (0.8, 0, 0, 1)  # Rouge pour stock bas
            stock_text = f"Stock: {stock_actuel} (⚠️ Stock bas)"
        else:
            stock_color = (0, 0.7, 0, 1)  # Vert pour stock OK
            stock_text = f"Stock: {stock_actuel} unités"
        
        stock_label = MDLabel(
            text=stock_text,
            font_style="Body2",
            theme_text_color="Custom",
            text_color=stock_color,
            size_hint_y=None,
            height="20dp"
        )
        
        # Statut actif/inactif
        statut = "Actif" if product_data.get('actif', True) else "Inactif"
        statut_color = (0, 0.7, 0, 1) if product_data.get('actif', True) else (0.8, 0, 0, 1)
        
        statut_label = MDLabel(
            text=f"Statut: {statut}",
            font_style="Caption",
            theme_text_color="Custom",
            text_color=statut_color,
            size_hint_y=None,
            height="16dp"
        )
        
        info_layout.add_widget(ref_label)
        info_layout.add_widget(prix_label)
        info_layout.add_widget(stock_label)
        info_layout.add_widget(statut_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(info_layout)
        self.add_widget(layout)


class ProductFormDialog(MDDialog):
    """Dialog pour ajouter/modifier un produit - Version simplifiée"""
    
    def __init__(self, product_data=None, on_save_callback=None, **kwargs):
        self.product_data = product_data or {}
        self.on_save_callback = on_save_callback
        
        # Créer le contenu du formulaire
        content_cls = self._create_form_content()
        
        # Boutons
        buttons = [
            MDFlatButton(
                text="ANNULER",
                on_release=self.dismiss
            ),
            MDRaisedButton(
                text="ENREGISTRER",
                on_release=self.save_product
            )
        ]
        
        title = "Modifier le produit" if product_data else "Nouveau produit"
        
        super().__init__(
            title=title,
            type="custom",
            content_cls=content_cls,
            buttons=buttons,
            size_hint=(0.9, None),
            height="600dp",
            **kwargs
        )
        
        # Configuration de la navigation par tabulation
        Clock.schedule_once(self._setup_tab_navigation, 0.1)
    
    def _create_form_content(self):
        """Créer le contenu du formulaire"""
        form_layout = MDBoxLayout(
            orientation='vertical', 
            spacing="8dp", 
            # adaptive_height=True,
            size_hint_y=None,
            height="800",
            padding="8dp"
        )
        
        # Champs principaux
        self.nom_field = MDTextField(
            hint_text="Nom du produit *",
            text=self.product_data.get('nom', ''),
            required=True
        )
        
        self.description_field = MDTextField(
            hint_text="Description",
            text=self.product_data.get('description', ''),
            multiline=True,
            max_height="80dp"
        )
        
        self.reference_field = MDTextField(
            hint_text="Référence *",
            text=self.product_data.get('reference', ''),
            required=True
        )
        
        # Code-barres simplifié
        self.code_barre_field = MDTextField(
            hint_text="Code-barres",
            text=self.product_data.get('code_barre', '')
        )
        
        # Catégorie - Liste déroulante native
        from kivymd.uix.menu import MDDropdownMenu
        from kivymd.uix.button import MDRaisedButton
        
        # Container pour la catégorie
        category_container = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height="80dp",
            spacing="4dp"
        )
        
        # Label pour la catégorie
        category_label = MDLabel(
            text="📂 Catégorie",
            font_style="Caption",
            theme_text_color="Primary",
            size_hint_y=None,
            height="20dp"
        )
        
        # Bouton de sélection de catégorie
        self.category_button = MDRaisedButton(
            text="🔄 Chargement des catégories...",
            size_hint_y=None,
            height="48dp",
            on_release=self.show_categories_menu,
            md_bg_color=[0.2, 0.6, 1, 1],  # Bleu
            theme_icon_color="Custom",
            icon_color=[1, 1, 1, 1]
        )
        
        category_container.add_widget(category_label)
        category_container.add_widget(self.category_button)
        
        self.selected_category_id = self.product_data.get('categorie_id', None)
        self.categories_menu = None
        self.categories_loaded = False
        self.categories_data = []
        
        # Charger les catégories et définir la valeur initiale
        self.load_categories_for_dropdown()
        
        # Prix
        self.prix_achat_field = MDTextField(
            hint_text="Prix d'achat (DH)",
            text=str(self.product_data.get('prix_achat', 0)),
            input_filter="float"
        )
        
        self.prix_vente_field = MDTextField(
            hint_text="Prix de vente (DH) *",
            text=str(self.product_data.get('prix_vente', 0)),
            input_filter="float",
            required=True
        )
        
        # Stock
        self.stock_actuel_field = MDTextField(
            hint_text="Stock actuel",
            text=str(self.product_data.get('stock_actuel', 0)),
            input_filter="int"
        )
        
        self.stock_minimum_field = MDTextField(
            hint_text="Stock minimum",
            text=str(self.product_data.get('stock_minimum', 0)),
            input_filter="int"
        )
        
        # TVA
        self.tva_field = MDTextField(
            hint_text="TVA (%)",
            text=str(self.product_data.get('tva', 20.0)),
            input_filter="float"
        )
        
        # Checkbox pour actif/inactif
        checkbox_layout = MDBoxLayout(
            orientation='horizontal', 
            size_hint_y=None, 
            height="48dp"
        )
        
        checkbox_label = MDLabel(
            text="Produit actif", 
            size_hint_x=0.7
        )
        
        self.actif_checkbox = MDCheckbox(
            active=bool(self.product_data.get('actif', True)),
            size_hint_x=0.3
        )
        
        checkbox_layout.add_widget(checkbox_label)
        checkbox_layout.add_widget(self.actif_checkbox)
        
        # Ordre des champs pour la navigation par tabulation
        self.fields_order = [
            self.nom_field,
            self.description_field,
            self.reference_field,
            self.code_barre_field,
            self.prix_achat_field,
            self.prix_vente_field,
            self.stock_actuel_field,
            self.stock_minimum_field,
            self.tva_field
        ]
        
        # Ajout des champs au formulaire
        form_layout.add_widget(self.nom_field)
        form_layout.add_widget(self.description_field)
        form_layout.add_widget(self.reference_field)
        form_layout.add_widget(self.code_barre_field)
        form_layout.add_widget(category_container)  # Container avec bouton de catégorie
        form_layout.add_widget(self.prix_achat_field)
        form_layout.add_widget(self.prix_vente_field)
        form_layout.add_widget(self.stock_actuel_field)
        form_layout.add_widget(self.stock_minimum_field)
        form_layout.add_widget(self.tva_field)
        form_layout.add_widget(checkbox_layout)
        
        return form_layout
    
    def _setup_tab_navigation(self, dt):
        """Configure la navigation par tabulation entre les champs"""
        try:
            for i, field in enumerate(self.fields_order):
                if hasattr(field, 'bind'):
                    field.bind(on_text_validate=self._on_tab_pressed)
                    field.tab_index = i
            
            # Focus sur le premier champ
            if self.fields_order:
                self.fields_order[0].focus = True
        except Exception as e:
            print(f"Erreur navigation tabulation: {e}")
    
    def _on_tab_pressed(self, instance):
        """Gérer la navigation par tabulation"""
        try:
            current_index = getattr(instance, 'tab_index', -1)
            if current_index >= 0 and current_index < len(self.fields_order) - 1:
                next_field = self.fields_order[current_index + 1]
                next_field.focus = True
        except Exception as e:
            print(f"Erreur navigation tabulation: {e}")
    
    def save_product(self, *args):
        """Enregistrer le produit"""
        # Validation basique
        if not self.nom_field.text.strip():
            self.nom_field.error = True
            self.nom_field.helper_text = "Le nom est obligatoire"
            return
        
        if not self.reference_field.text.strip():
            self.reference_field.error = True
            self.reference_field.helper_text = "La référence est obligatoire"
            return
        
        try:
            prix_vente = float(self.prix_vente_field.text or 0)
            if prix_vente <= 0:
                self.prix_vente_field.error = True
                self.prix_vente_field.helper_text = "Le prix de vente doit être supérieur à 0"
                return
        except ValueError:
            self.prix_vente_field.error = True
            self.prix_vente_field.helper_text = "Prix de vente invalide"
            return
        
        # Préparer les données
        product_data = {
            'nom': self.nom_field.text.strip(),
            'description': self.description_field.text.strip(),
            'reference': self.reference_field.text.strip(),
            'code_barre': self.code_barre_field.text.strip(),
            'categorie_id': self.selected_category_id,
            'prix_achat': float(self.prix_achat_field.text or 0),
            'prix_vente': float(self.prix_vente_field.text or 0),
            'stock_actuel': int(self.stock_actuel_field.text or 0),
            'stock_minimum': int(self.stock_minimum_field.text or 0),
            'tva': float(self.tva_field.text or 20.0),
            'actif': self.actif_checkbox.active
        }
        
        # Ajouter l'ID si c'est une modification
        if self.product_data.get('id'):
            product_data['id'] = self.product_data['id']
        
        # Appeler le callback
        if self.on_save_callback:
            self.on_save_callback(product_data)
        
        self.dismiss()
    
    def load_categories_for_dropdown(self):
        """Charger les catégories pour la liste déroulante"""
        def load_data():
            try:
                app = MDApp.get_running_app()
                if hasattr(app, 'db_manager'):
                    from database.db_manager import get_all_categories
                    categories = get_all_categories(app.db_manager)
                    
                    print(f"📂 {len(categories)} catégories chargées pour la liste déroulante")
                    
                    # Mettre à jour l'interface dans le thread principal
                    Clock.schedule_once(lambda dt: self.setup_categories_menu(categories), 0)
                else:
                    print("❌ Gestionnaire de base de données non disponible")
                    Clock.schedule_once(lambda dt: self.setup_categories_menu_error("DB non disponible"), 0)
                    
            except Exception as e:
                print(f"❌ Erreur lors du chargement des catégories: {e}")
                Clock.schedule_once(lambda dt: self.setup_categories_menu_error(str(e)), 0)
        
        # Charger dans un thread séparé
        threading.Thread(target=load_data, daemon=True).start()
    
    def setup_categories_menu(self, categories):
        """Configurer le menu déroulant des catégories"""
        try:
            self.categories_data = categories
            
            # Créer les éléments du menu avec comptage des produits
            menu_items = [
                {
                    "text": "📭 Aucune catégorie",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda: self.select_category(None, "Aucune catégorie")
                }
            ]
            
            # Trier les catégories par nom
            sorted_categories = sorted(categories, key=lambda x: x.get('nom', ''))
            
            for category in sorted_categories:
                menu_items.append({
                    "text": f"📂 {category.get('nom', 'Sans nom')}",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda cat=category: self.select_category(cat['id'], cat['nom'])
                })
            
            # Créer le menu déroulant avec le bouton comme caller
            self.categories_menu = MDDropdownMenu(
                caller=self.category_button,
                items=menu_items,
                width_mult=4,
                max_height="300dp"
            )
            
            self.categories_loaded = True
            
            # Définir la valeur initiale si on modifie un produit
            if self.selected_category_id:
                category_found = False
                for category in categories:
                    if category['id'] == self.selected_category_id:
                        self.category_button.text = f"📂 {category['nom']}"
                        self.category_button.md_bg_color = [0, 0.7, 0, 1]  # Vert pour sélectionné
                        category_found = True
                        break
                
                if not category_found:
                    self.category_button.text = "⚠️ Catégorie introuvable"
                    self.category_button.md_bg_color = [0.8, 0, 0, 1]  # Rouge pour erreur
                    self.selected_category_id = None
            else:
                self.category_button.text = "📭 Aucune catégorie"
                self.category_button.md_bg_color = [0.5, 0.5, 0.5, 1]  # Gris pour aucune
            
            print(f"✅ Menu des catégories configuré avec {len(categories)} options")
            
        except Exception as e:
            print(f"❌ Erreur lors de la configuration du menu: {e}")
            self.setup_categories_menu_error(str(e))
    
    def setup_categories_menu_error(self, error_msg):
        """Gérer les erreurs de chargement des catégories"""
        self.category_button.text = "❌ Erreur de chargement"
        self.category_button.md_bg_color = [0.8, 0, 0, 1]  # Rouge pour erreur
        self.categories_loaded = False
        
        # Créer un menu minimal avec option de rechargement
        menu_items = [
            {
                "text": "🔄 Recharger les catégories",
                "viewclass": "OneLineListItem",
                "on_release": lambda: self.reload_categories()
            },
            {
                "text": "📭 Aucune catégorie",
                "viewclass": "OneLineListItem",
                "on_release": lambda: self.select_category(None, "Aucune catégorie")
            }
        ]
        
        self.categories_menu = MDDropdownMenu(
            caller=self.category_button,
            items=menu_items,
            width_mult=4,
            max_height="200dp"
        )
    
    def reload_categories(self):
        """Recharger les catégories"""
        self.category_button.text = "🔄 Rechargement..."
        self.category_button.md_bg_color = [0.2, 0.6, 1, 1]  # Bleu pour chargement
        if self.categories_menu:
            self.categories_menu.dismiss()
        self.load_categories_for_dropdown()
    
    def show_categories_menu(self, instance):
        """Afficher le menu des catégories quand on clique sur le bouton"""
        if not self.categories_loaded:
            # Recharger si pas encore chargé
            self.reload_categories()
            return
        
        if self.categories_menu:
            self.categories_menu.open()
        else:
            # Recharger si menu non disponible
            self.reload_categories()
    
    def select_category(self, category_id, category_name):
        """Sélectionner une catégorie"""
        self.selected_category_id = category_id
        
        if category_id:
            self.category_button.text = f"📂 {category_name}"
            self.category_button.md_bg_color = [0, 0.7, 0, 1]  # Vert pour sélectionné
            print(f"✅ Catégorie sélectionnée: {category_name} (ID: {category_id})")
        else:
            self.category_button.text = "📭 Aucune catégorie"
            self.category_button.md_bg_color = [0.5, 0.5, 0.5, 1]  # Gris pour aucune
            print("✅ Aucune catégorie sélectionnée")
        
        if self.categories_menu:
            self.categories_menu.dismiss()


class ProductsScreen(MDScreen):
    """Écran de gestion des produits - Version simplifiée"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "products"
        self.products = []
        self.filtered_products = []
        self.build_ui()
    
    def build_ui(self):
        """Construire l'interface utilisateur"""
        layout = MDBoxLayout(orientation='vertical', padding="16dp", spacing="16dp")
        
        # En-tête avec titre et boutons
        header_layout = MDBoxLayout(orientation='horizontal', size_hint_y=None, height="56dp")
        
        title_label = MDLabel(
            text="Gestion des Produits",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_x=0.4
        )
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(orientation='horizontal', size_hint_x=0.6, spacing="8dp")
        
        add_btn = MDRaisedButton(
            text="Nouveau Produit",
            icon="plus",
            on_release=self.add_product,
            size_hint_x=0.3
        )
        
        export_btn = MDRaisedButton(
            text="Exporter",
            icon="download",
            on_release=self.export_products,
            size_hint_x=0.2
        )
        
        low_stock_btn = MDRaisedButton(
            text="Stock Bas",
            icon="alert",
            on_release=self.show_low_stock,
            size_hint_x=0.15
        )
        
        categories_btn = MDRaisedButton(
            text="📂 Catégories",
            icon="folder",
            on_release=self.show_categories_filter,
            size_hint_x=0.15
        )
        
        buttons_layout.add_widget(add_btn)
        buttons_layout.add_widget(export_btn)
        buttons_layout.add_widget(low_stock_btn)
        buttons_layout.add_widget(categories_btn)
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(buttons_layout)
        
        # Barre de recherche améliorée
        self.search_field = MDTextField(
            hint_text="🔍 Rechercher par nom, référence, code-barres ou catégorie...",
            icon_right="magnify",
            size_hint_y=None,
            height="56dp",
            helper_text="Tapez pour rechercher dans tous les champs (y compris les catégories)",
            helper_text_mode="persistent"
        )
        self.search_field.bind(text=self.filter_products)
        
        # Zone de contenu avec scroll
        self.scroll_view = MDScrollView()
        self.products_layout = MDGridLayout(
            cols=1,
            spacing="8dp",
            adaptive_height=True,
            padding="8dp"
        )
        
        self.scroll_view.add_widget(self.products_layout)
        
        # Assemblage final
        layout.add_widget(header_layout)
        layout.add_widget(self.search_field)
        layout.add_widget(self.scroll_view)
        
        self.add_widget(layout)
        
        # Charger les produits
        Clock.schedule_once(self.load_products, 0.1)
    
    def load_products(self, dt=None):
        """Charger les produits depuis la base de données avec leurs catégories"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                # Charger les produits avec leurs catégories via une jointure
                products_with_categories = app.db_manager.execute_query("""
                    SELECT p.*, c.nom as categorie_nom, c.description as categorie_description
                    FROM produits p
                    LEFT JOIN categories c ON p.categorie_id = c.id
                    WHERE p.actif = 1
                    ORDER BY p.nom
                """)
                
                self.products = products_with_categories or []
                self.filtered_products = self.products.copy()
                
                print(f"📦 {len(self.products)} produits chargés avec informations de catégorie")
                
                self.update_products_display()
            else:
                # Données de test si pas de base de données
                self.products = [
                    {
                        'id': 1,
                        'nom': 'Produit Test 1',
                        'reference': 'TEST001',
                        'prix_vente': 100.0,
                        'stock_actuel': 10,
                        'stock_minimum': 5,
                        'actif': True
                    },
                    {
                        'id': 2,
                        'nom': 'Produit Test 2',
                        'reference': 'TEST002',
                        'prix_vente': 250.0,
                        'stock_actuel': 2,
                        'stock_minimum': 5,
                        'actif': True
                    }
                ]
                self.filtered_products = self.products.copy()
                self.update_products_display()
        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")
    
    def update_products_display(self):
        """Mettre à jour l'affichage des produits"""
        self.products_layout.clear_widgets()
        
        if not self.filtered_products:
            no_products_label = MDLabel(
                text="Aucun produit trouvé",
                halign="center",
                font_style="H6",
                theme_text_color="Secondary"
            )
            self.products_layout.add_widget(no_products_label)
            return
        
        for product in self.filtered_products:
            card = ProductCard(
                product_data=product,
                on_edit_callback=self.edit_product,
                on_delete_callback=self.delete_product
            )
            self.products_layout.add_widget(card)
    
    def filter_products(self, instance, text):
        """Filtrer les produits selon le texte de recherche (inclut les catégories)"""
        if not text.strip():
            self.filtered_products = self.products.copy()
        else:
            search_text = text.lower()
            self.filtered_products = [
                product for product in self.products
                if (search_text in product.get('nom', '').lower() or
                    search_text in product.get('reference', '').lower() or
                    search_text in product.get('code_barre', '').lower() or
                    search_text in product.get('categorie_nom', '').lower())
            ]
        
        print(f"🔍 Recherche '{text}': {len(self.filtered_products)} produits trouvés")
        self.update_products_display()
    
    def add_product(self, *args):
        """Ajouter un nouveau produit"""
        dialog = ProductFormDialog(
            on_save_callback=self.save_product
        )
        dialog.open()
    
    def edit_product(self, product_data):
        """Éditer un produit existant"""
        dialog = ProductFormDialog(
            product_data=product_data,
            on_save_callback=self.save_product
        )
        dialog.open()
    
    def save_product(self, product_data):
        """Sauvegarder un produit"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                # Import des fonctions de base de données
                from database.db_manager import add_product, update_product
                
                if product_data.get('id'):
                    # Modification
                    update_product(app.db_manager, product_data)
                else:
                    # Nouveau produit
                    add_product(app.db_manager, product_data)
                
                # Recharger les produits
                self.load_products()
            else:
                # Mode test sans base de données
                if product_data.get('id'):
                    # Modification
                    for i, product in enumerate(self.products):
                        if product['id'] == product_data['id']:
                            self.products[i] = product_data
                            break
                else:
                    # Nouveau produit
                    product_data['id'] = len(self.products) + 1
                    self.products.append(product_data)
                
                self.filtered_products = self.products.copy()
                self.update_products_display()
            
            print(f"Produit sauvegardé: {product_data.get('nom')}")
            
        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
    
    def delete_product(self, product_data):
        """Supprimer un produit"""
        # TODO: Ajouter une confirmation
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'db_manager'):
                # Import de la fonction de suppression
                from database.db_manager import delete_product
                delete_product(app.db_manager, product_data['id'])
                self.load_products()
            else:
                # Mode test
                self.products = [p for p in self.products if p['id'] != product_data['id']]
                self.filtered_products = self.products.copy()
                self.update_products_display()
            
            print(f"Produit supprimé: {product_data.get('nom')}")
            
        except Exception as e:
            print(f"Erreur lors de la suppression: {e}")
    
    def export_products(self, *args):
        """Exporter les produits en CSV"""
        try:
            import csv
            from datetime import datetime
            
            filename = f"produits_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['nom', 'reference', 'code_barre', 'prix_achat', 'prix_vente', 
                             'stock_actuel', 'stock_minimum', 'tva', 'actif']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for product in self.products:
                    row = {field: product.get(field, '') for field in fieldnames}
                    writer.writerow(row)
            
            print(f"Export réussi: {filename}")
            
        except Exception as e:
            print(f"Erreur lors de l'export: {e}")
    
    def show_low_stock(self, *args):
        """Afficher les produits avec stock bas"""
        low_stock_products = [
            product for product in self.products
            if (product.get('stock_actuel', 0) <= product.get('stock_minimum', 0) 
                and product.get('stock_minimum', 0) > 0)
        ]
        
        self.filtered_products = low_stock_products
        self.update_products_display()
        
        if not low_stock_products:
            print("Aucun produit avec stock bas")
        else:
            print(f"{len(low_stock_products)} produit(s) avec stock bas")
    
    def show_categories_filter(self, *args):
        """Afficher le filtre par catégorie"""
        def load_categories():
            try:
                app = MDApp.get_running_app()
                if hasattr(app, 'db_manager'):
                    from database.db_manager import get_all_categories
                    categories = get_all_categories(app.db_manager)
                    
                    # Compter les produits par catégorie
                    categories_with_count = []
                    for category in categories:
                        count = len([p for p in self.products if p.get('categorie_id') == category['id']])
                        if count > 0:  # Seulement les catégories avec des produits
                            categories_with_count.append({
                                'id': category['id'],
                                'nom': category['nom'],
                                'count': count
                            })
                    
                    # Ajouter l'option "Tous les produits" et "Sans catégorie"
                    all_count = len(self.products)
                    no_category_count = len([p for p in self.products if not p.get('categorie_id')])
                    
                    filter_options = [
                        {'id': 'all', 'nom': 'Tous les produits', 'count': all_count}
                    ]
                    
                    if no_category_count > 0:
                        filter_options.append({
                            'id': 'none', 'nom': 'Sans catégorie', 'count': no_category_count
                        })
                    
                    filter_options.extend(sorted(categories_with_count, key=lambda x: x['nom']))
                    
                    Clock.schedule_once(lambda dt: self.show_category_filter_dialog(filter_options), 0)
                else:
                    print("❌ Gestionnaire de base de données non disponible")
                    
            except Exception as e:
                print(f"❌ Erreur lors du chargement des catégories: {e}")
        
        # Charger dans un thread séparé
        threading.Thread(target=load_categories, daemon=True).start()
    
    def show_category_filter_dialog(self, filter_options):
        """Afficher le dialog de filtre par catégorie"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.list import OneLineAvatarIconListItem, IconLeftWidget
        from kivymd.uix.list import MDList
        
        # Créer la liste des options
        filter_list = MDList()
        
        for option in filter_options:
            item = OneLineAvatarIconListItem(
                text=f"{option['nom']} ({option['count']} produits)",
                on_release=lambda x, opt=option: self.apply_category_filter(opt)
            )
            
            if option['id'] == 'all':
                item.add_widget(IconLeftWidget(icon="view-list"))
            elif option['id'] == 'none':
                item.add_widget(IconLeftWidget(icon="folder-remove"))
            else:
                item.add_widget(IconLeftWidget(icon="folder"))
            
            filter_list.add_widget(item)
        
        # Créer le dialog
        self.category_filter_dialog = MDDialog(
            title="📂 Filtrer par Catégorie",
            type="custom",
            content_cls=filter_list,
            buttons=[
                MDRaisedButton(
                    text="Fermer",
                    theme_icon_color="Custom",
                    icon_color=[1, 1, 1, 1],
                    md_bg_color=[0.5, 0.5, 0.5, 1],
                    on_release=self.close_category_filter_dialog
                )
            ]
        )
        self.category_filter_dialog.open()
    
    def apply_category_filter(self, option):
        """Appliquer le filtre par catégorie"""
        if option['id'] == 'all':
            # Tous les produits
            self.filtered_products = self.products.copy()
            filter_text = "Tous les produits"
        elif option['id'] == 'none':
            # Produits sans catégorie
            self.filtered_products = [p for p in self.products if not p.get('categorie_id')]
            filter_text = "Produits sans catégorie"
        else:
            # Produits d'une catégorie spécifique
            self.filtered_products = [p for p in self.products if p.get('categorie_id') == option['id']]
            filter_text = f"Catégorie: {option['nom']}"
        
        # Mettre à jour l'affichage
        self.update_products_display()
        
        # Mettre à jour le champ de recherche pour indiquer le filtre actif
        self.search_field.text = ""
        self.search_field.hint_text = f"🔍 {filter_text} - Rechercher..."
        
        print(f"📂 Filtre appliqué: {filter_text} ({len(self.filtered_products)} produits)")
        
        # Fermer le dialog
        self.close_category_filter_dialog()
    
    def close_category_filter_dialog(self, *args):
        """Fermer le dialog de filtre par catégorie"""
        if hasattr(self, 'category_filter_dialog') and self.category_filter_dialog:
            self.category_filter_dialog.dismiss()
            self.category_filter_dialog = None