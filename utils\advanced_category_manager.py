"""
Gestionnaire avancé de catégories avec fonctionnalités étendues
"""

from database.db_manager import DatabaseManager
from datetime import datetime
import json

class AdvancedCategoryManager:
    """Gestionnaire avancé pour les catégories avec fonctionnalités étendues"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def get_category_statistics(self):
        """Obtenir des statistiques détaillées sur les catégories"""
        try:
            if not self.db_manager.connect():
                return None
            
            # Statistiques générales
            stats = {}
            
            # Total catégories
            total_result = self.db_manager.execute_query("SELECT COUNT(*) as total FROM categories")
            stats['total_categories'] = total_result[0]['total'] if total_result else 0
            
            # Catégories avec produits
            with_products_result = self.db_manager.execute_query("""
                SELECT COUNT(DISTINCT c.id) as count
                FROM categories c
                INNER JOIN produits p ON c.id = p.categorie_id
                WHERE p.actif = 1
            """)
            stats['categories_with_products'] = with_products_result[0]['count'] if with_products_result else 0
            
            # Catégories vides
            stats['empty_categories'] = stats['total_categories'] - stats['categories_with_products']
            
            # Total produits
            total_products_result = self.db_manager.execute_query("""
                SELECT COUNT(*) as total FROM produits WHERE actif = 1
            """)
            stats['total_products'] = total_products_result[0]['total'] if total_products_result else 0
            
            # Produits sans catégorie
            no_category_result = self.db_manager.execute_query("""
                SELECT COUNT(*) as count FROM produits WHERE categorie_id IS NULL AND actif = 1
            """)
            stats['products_without_category'] = no_category_result[0]['count'] if no_category_result else 0
            
            # Top 5 catégories par nombre de produits
            top_categories_result = self.db_manager.execute_query("""
                SELECT c.nom, COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom
                ORDER BY products_count DESC
                LIMIT 5
            """)
            stats['top_categories'] = top_categories_result if top_categories_result else []
            
            return stats
            
        except Exception as e:
            print(f"❌ Erreur statistiques catégories: {e}")
            return None
        
        finally:
            self.db_manager.disconnect()
    
    def export_categories_data(self, format='json'):
        """Exporter les données des catégories"""
        try:
            if not self.db_manager.connect():
                return None
            
            # Récupérer toutes les catégories avec leurs produits
            categories_data = self.db_manager.execute_query("""
                SELECT c.*, 
                       COUNT(p.id) as products_count,
                       GROUP_CONCAT(p.nom) as products_list
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom, c.description, c.date_creation
                ORDER BY c.nom ASC
            """)
            
            if not categories_data:
                return None
            
            # Préparer les données pour l'export
            export_data = {
                'export_date': datetime.now().isoformat(),
                'total_categories': len(categories_data),
                'categories': []
            }
            
            for category in categories_data:
                cat_data = {
                    'id': category['id'],
                    'nom': category['nom'],
                    'description': category.get('description', ''),
                    'date_creation': category.get('date_creation', ''),
                    'products_count': category.get('products_count', 0),
                    'products_list': category.get('products_list', '').split(',') if category.get('products_list') else []
                }
                export_data['categories'].append(cat_data)
            
            if format == 'json':
                return json.dumps(export_data, indent=2, ensure_ascii=False)
            else:
                return export_data
            
        except Exception as e:
            print(f"❌ Erreur export catégories: {e}")
            return None
        
        finally:
            self.db_manager.disconnect()
    
    def import_categories_data(self, data):
        """Importer des données de catégories"""
        try:
            if not self.db_manager.connect():
                return False
            
            if isinstance(data, str):
                data = json.loads(data)
            
            imported_count = 0
            
            for category in data.get('categories', []):
                # Vérifier si la catégorie existe déjà
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (category['nom'],)
                )
                
                if not existing:
                    # Créer la catégorie
                    cursor = self.db_manager.connection.cursor()
                    cursor.execute(
                        "INSERT INTO categories (nom, description) VALUES (?, ?)",
                        (category['nom'], category.get('description', ''))
                    )
                    self.db_manager.connection.commit()
                    imported_count += 1
            
            return imported_count
            
        except Exception as e:
            print(f"❌ Erreur import catégories: {e}")
            return False
        
        finally:
            self.db_manager.disconnect()
    
    def optimize_categories(self):
        """Optimiser les catégories (supprimer les vides, réorganiser)"""
        try:
            if not self.db_manager.connect():
                return False
            
            optimizations = []
            
            # 1. Identifier les catégories vides
            empty_categories = self.db_manager.execute_query("""
                SELECT c.id, c.nom
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom
                HAVING COUNT(p.id) = 0
            """)
            
            if empty_categories:
                optimizations.append(f"Trouvé {len(empty_categories)} catégorie(s) vide(s)")
            
            # 2. Identifier les doublons potentiels
            potential_duplicates = self.db_manager.execute_query("""
                SELECT nom, COUNT(*) as count
                FROM categories
                GROUP BY LOWER(nom)
                HAVING count > 1
            """)
            
            if potential_duplicates:
                optimizations.append(f"Trouvé {len(potential_duplicates)} doublon(s) potentiel(s)")
            
            return optimizations
            
        except Exception as e:
            print(f"❌ Erreur optimisation catégories: {e}")
            return []
        
        finally:
            self.db_manager.disconnect()
    
    def get_category_hierarchy(self):
        """Obtenir la hiérarchie des catégories (pour future extension)"""
        # Cette méthode peut être étendue pour supporter des sous-catégories
        try:
            if not self.db_manager.connect():
                return None
            
            categories = self.db_manager.execute_query("""
                SELECT c.*, COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom, c.description, c.date_creation
                ORDER BY c.nom ASC
            """)
            
            # Organiser en hiérarchie (pour l'instant, liste plate)
            hierarchy = {
                'root': {
                    'name': 'Toutes les catégories',
                    'children': []
                }
            }
            
            for category in categories or []:
                hierarchy['root']['children'].append({
                    'id': category['id'],
                    'name': category['nom'],
                    'description': category.get('description', ''),
                    'products_count': category.get('products_count', 0),
                    'children': []  # Pour future extension
                })
            
            return hierarchy
            
        except Exception as e:
            print(f"❌ Erreur hiérarchie catégories: {e}")
            return None
        
        finally:
            self.db_manager.disconnect()
