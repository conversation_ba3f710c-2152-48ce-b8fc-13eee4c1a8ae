"""
Gestionnaire de base de données SQLite pour l'application de gestion commerciale
"""

import sqlite3
import os
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple
import json


class DatabaseManager:
    """Gestionnaire principal de la base de données"""
    
    def __init__(self, db_path: str = None):
        """
        Initialisation du gestionnaire de base de données
        
        Args:
            db_path: Chemin vers le fichier de base de données
        """
        if db_path is None:
            # Créer le dossier data s'il n'existe pas
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
            os.makedirs(data_dir, exist_ok=True)
            db_path = os.path.join(data_dir, 'gescom.db')
        
        self.db_path = db_path
        self.connection = None
    
    def connect(self):
        """Établir la connexion à la base de données"""
        # Si déjà connecté, ne pas créer une nouvelle connexion
        if self.connection:
            return True
            
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # Pour accéder aux colonnes par nom
            return True
        except sqlite3.Error as e:
            print(f"Erreur de connexion à la base de données: {e}")
            return False
    
    def disconnect(self):
        """Fermer la connexion à la base de données"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def initialize_database(self):
        """Initialiser la base de données avec les tables nécessaires"""
        # Si c'est une base en mémoire et qu'on est déjà connecté, ne pas se reconnecter
        if self.db_path == ":memory:" and self.connection:
            pass  # Garder la connexion existante
        elif not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # Table des clients
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    prenom TEXT,
                    entreprise TEXT,
                    email TEXT UNIQUE,
                    telephone TEXT,
                    adresse TEXT,
                    ville TEXT,
                    code_postal TEXT,
                    pays TEXT DEFAULT 'France',
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    actif BOOLEAN DEFAULT 1
                )
            ''')
            
            # Table des fournisseurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fournisseurs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    contact_nom TEXT,
                    email TEXT,
                    telephone TEXT,
                    adresse TEXT,
                    ville TEXT,
                    code_postal TEXT,
                    pays TEXT DEFAULT 'France',
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    actif BOOLEAN DEFAULT 1
                )
            ''')
            
            # Table des catégories de produits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL UNIQUE,
                    description TEXT,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des produits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS produits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    description TEXT,
                    reference TEXT UNIQUE,
                    code_barre TEXT,
                    categorie_id INTEGER,
                    fournisseur_id INTEGER,
                    prix_achat REAL DEFAULT 0,
                    prix_vente REAL NOT NULL,
                    stock_actuel INTEGER DEFAULT 0,
                    stock_minimum INTEGER DEFAULT 0,
                    tva REAL DEFAULT 20.0,
                    actif BOOLEAN DEFAULT 1,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (categorie_id) REFERENCES categories (id),
                    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
                )
            ''')
            
            # Table des ventes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ventes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero_facture TEXT UNIQUE NOT NULL,
                    client_id INTEGER,
                    date_vente TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    montant_ht REAL DEFAULT 0,
                    montant_tva REAL DEFAULT 0,
                    montant_ttc REAL DEFAULT 0,
                    statut TEXT DEFAULT 'En cours',
                    mode_paiement TEXT,
                    notes TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            ''')
            
            # Table des détails de vente
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS details_vente (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    vente_id INTEGER NOT NULL,
                    produit_id INTEGER NOT NULL,
                    quantite INTEGER NOT NULL,
                    prix_unitaire REAL NOT NULL,
                    remise REAL DEFAULT 0,
                    montant_ligne REAL NOT NULL,
                    FOREIGN KEY (vente_id) REFERENCES ventes (id) ON DELETE CASCADE,
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            ''')
            
            # Table des mouvements de stock
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mouvements_stock (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    produit_id INTEGER NOT NULL,
                    type_mouvement TEXT NOT NULL, -- 'ENTREE', 'SORTIE', 'AJUSTEMENT'
                    quantite INTEGER NOT NULL,
                    prix_unitaire REAL,
                    reference_document TEXT, -- Référence de la vente, achat, etc.
                    date_mouvement TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    commentaire TEXT,
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            ''')
            
            # Table des paramètres de l'application
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS parametres (
                    cle TEXT PRIMARY KEY,
                    valeur TEXT,
                    description TEXT,
                    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Index pour améliorer les performances
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_produits_reference ON produits(reference)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ventes_date ON ventes(date_vente)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ventes_client ON ventes(client_id)')
            
            self.connection.commit()
            print("Base de données initialisée avec succès")
            return True
            
        except sqlite3.Error as e:
            print(f"Erreur lors de l'initialisation de la base de données: {e}")
            if self.connection:
                self.connection.rollback()
            return False
        # Ne pas fermer automatiquement la connexion après initialisation
    
    def has_initial_data(self) -> bool:
        """Vérifier si la base de données contient des données initiales"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM clients")
            client_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM produits")
            product_count = cursor.fetchone()[0]
            
            return client_count > 0 or product_count > 0
            
        except sqlite3.Error:
            return False
        # Ne pas fermer automatiquement la connexion
    
    def create_sample_data(self):
        """Créer des données d'exemple pour la démonstration"""
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # Catégories d'exemple
            categories = [
                ("Électronique", "Produits électroniques et informatiques"),
                ("Vêtements", "Articles vestimentaires"),
                ("Alimentation", "Produits alimentaires"),
                ("Maison & Jardin", "Articles pour la maison et le jardin"),
                ("Sport & Loisirs", "Articles de sport et loisirs")
            ]
            
            for nom, description in categories:
                cursor.execute(
                    "INSERT OR IGNORE INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
            
            # Fournisseurs d'exemple
            fournisseurs = [
                ("TechSupply", "Jean Dupont", "<EMAIL>", "***********.89"),
                ("FashionWorld", "Marie Martin", "<EMAIL>", "***********.32"),
                ("AlimFresh", "Pierre Durand", "<EMAIL>", "***********.44")
            ]
            
            for nom, contact, email, tel in fournisseurs:
                cursor.execute(
                    "INSERT OR IGNORE INTO fournisseurs (nom, contact_nom, email, telephone) VALUES (?, ?, ?, ?)",
                    (nom, contact, email, tel)
                )
            
            # Clients d'exemple
            clients = [
                ("Dubois", "Antoine", "Entreprise ABC", "<EMAIL>", "***********.78"),
                ("Moreau", "Sophie", "Boutique Sophie", "<EMAIL>", "***********.32"),
                ("Bernard", "Lucas", None, "<EMAIL>", "***********.44"),
                ("Petit", "Emma", "StartupXYZ", "<EMAIL>", "06.55.66.77.88")
            ]
            
            for nom, prenom, entreprise, email, tel in clients:
                cursor.execute(
                    "INSERT OR IGNORE INTO clients (nom, prenom, entreprise, email, telephone) VALUES (?, ?, ?, ?, ?)",
                    (nom, prenom, entreprise, email, tel)
                )
            
            # Produits d'exemple
            produits = [
                ("Ordinateur Portable", "Laptop 15 pouces", "LAPTOP001", None, 1, 1, 800.00, 1200.00, 10, 2),
                ("Smartphone", "Téléphone dernière génération", "PHONE001", None, 1, 1, 400.00, 699.00, 25, 5),
                ("T-shirt Coton", "T-shirt 100% coton", "TSHIRT001", None, 2, 2, 8.00, 19.99, 50, 10),
                ("Jean Slim", "Jean coupe slim", "JEAN001", None, 2, 2, 25.00, 59.99, 30, 5),
                ("Café Bio", "Café biologique 1kg", "CAFE001", None, 3, 3, 12.00, 24.99, 100, 20)
            ]
            
            for nom, desc, ref, cb, cat_id, four_id, prix_achat, prix_vente, stock, stock_min in produits:
                cursor.execute(
                    """INSERT OR IGNORE INTO produits 
                       (nom, description, reference, code_barre, categorie_id, fournisseur_id, 
                        prix_achat, prix_vente, stock_actuel, stock_minimum) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (nom, desc, ref, cb, cat_id, four_id, prix_achat, prix_vente, stock, stock_min)
                )
            
            # Paramètres par défaut
            parametres = [
                ("entreprise_nom", "GesComPro", "Nom de l'entreprise"),
                ("entreprise_adresse", "123 Rue de la Gestion", "Adresse de l'entreprise"),
                ("entreprise_telephone", "***********.89", "Téléphone de l'entreprise"),
                ("entreprise_email", "<EMAIL>", "Email de l'entreprise"),
                ("tva_defaut", "20.0", "Taux de TVA par défaut"),
                ("devise", "MAD", "Devise utilisée"),
                ("format_facture", "FAC-{YYYY}-{MM}-{NNNN}", "Format des numéros de facture")
            ]
            
            for cle, valeur, description in parametres:
                cursor.execute(
                    "INSERT OR IGNORE INTO parametres (cle, valeur, description) VALUES (?, ?, ?)",
                    (cle, valeur, description)
                )
            
            self.connection.commit()
            print("Données d'exemple créées avec succès")
            return True
            
        except sqlite3.Error as e:
            print(f"Erreur lors de la création des données d'exemple: {e}")
            if self.connection:
                self.connection.rollback()
            return False
        # Ne pas fermer automatiquement la connexion
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """
        Exécuter une requête SELECT et retourner les résultats
        
        Args:
            query: Requête SQL
            params: Paramètres de la requête
            
        Returns:
            Liste des résultats sous forme de dictionnaires
        """
        if not self.connect():
            return []
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Convertir les résultats en liste de dictionnaires
            columns = [description[0] for description in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
            
        except sqlite3.Error as e:
            print(f"Erreur lors de l'exécution de la requête: {e}")
            return []
        # Ne pas fermer automatiquement la connexion pour permettre les transactions
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """
        Exécuter une requête INSERT, UPDATE ou DELETE
        
        Args:
            query: Requête SQL
            params: Paramètres de la requête
            
        Returns:
            True si succès, False sinon
        """
        if not self.connect():
            return False
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"Erreur lors de l'exécution de la requête: {e}")
            if self.connection:
                self.connection.rollback()
            return False
        # Ne pas fermer automatiquement la connexion pour permettre les transactions
    
    def execute_insert(self, query: str, params: tuple = None) -> Optional[int]:
        """
        Exécuter une requête INSERT et retourner l'ID inséré
        
        Args:
            query: Requête SQL INSERT
            params: Paramètres de la requête
            
        Returns:
            ID de l'enregistrement inséré ou None si erreur
        """
        if not self.connect():
            return None
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return cursor.lastrowid
            
        except sqlite3.Error as e:
            print(f"Erreur lors de l'exécution de la requête: {e}")
            if self.connection:
                self.connection.rollback()
            return None
        # Ne pas fermer automatiquement la connexion pour permettre les transactions
    
    def get_last_insert_id(self) -> Optional[int]:
        """Obtenir l'ID du dernier enregistrement inséré"""
        if self.connection:
            return self.connection.lastrowid
        return None
    
    def close(self):
        """Fermer la connexion à la base de données"""
        self.disconnect()


# Fonctions utilitaires pour les requêtes courantes

def get_all_clients(db_manager: DatabaseManager) -> List[Dict]:
    """Récupérer tous les clients actifs"""
    return db_manager.execute_query(
        "SELECT * FROM clients WHERE actif = 1 ORDER BY nom, prenom"
    )

def get_all_products(db_manager: DatabaseManager) -> List[Dict]:
    """Récupérer tous les produits actifs avec leurs catégories"""
    return db_manager.execute_query("""
        SELECT p.*, c.nom as categorie_nom, f.nom as fournisseur_nom
        FROM produits p
        LEFT JOIN categories c ON p.categorie_id = c.id
        LEFT JOIN fournisseurs f ON p.fournisseur_id = f.id
        WHERE p.actif = 1
        ORDER BY p.nom
    """)

def get_sales_summary(db_manager: DatabaseManager, start_date: str = None, end_date: str = None) -> Dict:
    """Récupérer un résumé des ventes"""
    where_clause = ""
    params = []
    
    if start_date and end_date:
        where_clause = "WHERE date_vente BETWEEN ? AND ?"
        params = [start_date, end_date]
    
    query = f"""
        SELECT 
            COUNT(*) as nombre_ventes,
            SUM(montant_ttc) as chiffre_affaires,
            AVG(montant_ttc) as panier_moyen
        FROM ventes
        {where_clause}
    """
    
    results = db_manager.execute_query(query, tuple(params) if params else None)
    return results[0] if results else {}

def get_low_stock_products(db_manager: DatabaseManager) -> List[Dict]:
    """Récupérer les produits avec un stock faible"""
    return db_manager.execute_query("""
        SELECT p.*, c.nom as categorie_nom
        FROM produits p
        LEFT JOIN categories c ON p.categorie_id = c.id
        WHERE p.stock_actuel <= p.stock_minimum AND p.actif = 1
        ORDER BY p.stock_actuel ASC
    """)

def add_product(db_manager: DatabaseManager, product_data: Dict) -> int:
    """Ajouter un nouveau produit"""
    query = """
        INSERT INTO produits (
            nom, description, reference, code_barre, prix_achat, prix_vente,
            stock_actuel, stock_minimum, tva, actif, categorie_id, fournisseur_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    params = (
        product_data.get('nom', ''),
        product_data.get('description', ''),
        product_data.get('reference', ''),
        product_data.get('code_barre', ''),
        product_data.get('prix_achat', 0),
        product_data.get('prix_vente', 0),
        product_data.get('stock_actuel', 0),
        product_data.get('stock_minimum', 0),
        product_data.get('tva', 20.0),
        1 if product_data.get('actif', True) else 0,
        product_data.get('categorie_id'),
        product_data.get('fournisseur_id')
    )
    
    cursor = db_manager.connection.cursor()
    cursor.execute(query, params)
    db_manager.connection.commit()
    return cursor.lastrowid

def update_product(db_manager: DatabaseManager, product_data: Dict) -> bool:
    """Mettre à jour un produit existant"""
    query = """
        UPDATE produits SET
            nom = ?, description = ?, reference = ?, code_barre = ?,
            prix_achat = ?, prix_vente = ?, stock_actuel = ?, stock_minimum = ?,
            tva = ?, actif = ?, categorie_id = ?, fournisseur_id = ?
        WHERE id = ?
    """
    
    params = (
        product_data.get('nom', ''),
        product_data.get('description', ''),
        product_data.get('reference', ''),
        product_data.get('code_barre', ''),
        product_data.get('prix_achat', 0),
        product_data.get('prix_vente', 0),
        product_data.get('stock_actuel', 0),
        product_data.get('stock_minimum', 0),
        product_data.get('tva', 20.0),
        1 if product_data.get('actif', True) else 0,
        product_data.get('categorie_id'),
        product_data.get('fournisseur_id'),
        product_data.get('id')
    )
    
    cursor = db_manager.connection.cursor()
    cursor.execute(query, params)
    db_manager.connection.commit()
    return True

def delete_product(db_manager: DatabaseManager, product_id: int) -> bool:
    """Supprimer un produit (soft delete - marquer comme inactif)"""
    query = "UPDATE produits SET actif = 0 WHERE id = ?"
    cursor = db_manager.connection.cursor()
    cursor.execute(query, (product_id,))
    db_manager.connection.commit()
    return True

# ==================== FONCTIONS VENTES ====================

def get_all_sales(db_manager: DatabaseManager) -> List[Dict]:
    """Récupérer toutes les ventes avec les informations client"""
    return db_manager.execute_query("""
        SELECT v.*, 
               c.nom as client_nom, 
               c.prenom as client_prenom,
               c.entreprise as client_entreprise
        FROM ventes v
        LEFT JOIN clients c ON v.client_id = c.id
        ORDER BY v.date_vente DESC
    """)

def get_sale_by_id(db_manager: DatabaseManager, sale_id: int) -> Dict:
    """Récupérer une vente par son ID avec ses détails"""
    # Récupérer la vente
    sales = db_manager.execute_query("""
        SELECT v.*, 
               c.nom as client_nom, 
               c.prenom as client_prenom,
               c.entreprise as client_entreprise,
               c.email as client_email,
               c.telephone as client_telephone
        FROM ventes v
        LEFT JOIN clients c ON v.client_id = c.id
        WHERE v.id = ?
    """, (sale_id,))
    
    if not sales:
        return {}
    
    sale = sales[0]
    
    # Récupérer les détails de la vente
    details = db_manager.execute_query("""
        SELECT dv.*, p.nom as produit_nom, p.reference as produit_reference
        FROM details_vente dv
        JOIN produits p ON dv.produit_id = p.id
        WHERE dv.vente_id = ?
        ORDER BY dv.id
    """, (sale_id,))
    
    sale['details'] = details
    return sale

def create_sale(db_manager: DatabaseManager, sale_data: Dict, sale_items: List[Dict]) -> int:
    """Créer une nouvelle vente avec ses détails"""
    try:
        # S'assurer que la connexion est active
        if not db_manager.connection:
            db_manager.connect()
        
        # Générer un numéro de facture unique
        import time
        import random
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)
        numero_facture = f"FAC-{timestamp}-{random_suffix}"
        
        # Calculer les totaux
        montant_ht = 0
        montant_tva = 0
        
        for item in sale_items:
            quantite = item.get('quantite', 1)
            prix_unitaire = item.get('prix_unitaire', 0)
            ligne_ht = quantite * prix_unitaire
            montant_ht += ligne_ht
            
            # Calculer la TVA (supposons 20% par défaut)
            tva_produit = item.get('product', {}).get('tva', 20.0)
            montant_tva += ligne_ht * (tva_produit / 100)
        
        montant_ttc = montant_ht + montant_tva
        
        # Insérer la vente
        query_vente = """
            INSERT INTO ventes (
                numero_facture, client_id, montant_ht, montant_tva, montant_ttc,
                statut, mode_paiement, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params_vente = (
            numero_facture,
            sale_data.get('client_id'),
            montant_ht,
            montant_tva,
            montant_ttc,
            sale_data.get('statut', 'En cours'),
            sale_data.get('mode_paiement', 'Espèces'),
            sale_data.get('notes', '')
        )
        
        # Exécuter l'insertion de la vente
        cursor = db_manager.connection.cursor()
        cursor.execute(query_vente, params_vente)
        vente_id = cursor.lastrowid
        
        # Insérer les détails de la vente
        query_detail = """
            INSERT INTO details_vente (
                vente_id, produit_id, quantite, prix_unitaire, montant_ligne
            ) VALUES (?, ?, ?, ?, ?)
        """
        
        for item in sale_items:
            quantite = item.get('quantite', 1)
            prix_unitaire = item.get('prix_unitaire', 0)
            montant_ligne = quantite * prix_unitaire
            
            params_detail = (
                vente_id,
                item.get('product', {}).get('id'),
                quantite,
                prix_unitaire,
                montant_ligne
            )
            
            cursor.execute(query_detail, params_detail)
            
            # Mettre à jour le stock du produit
            update_stock_query = """
                UPDATE produits 
                SET stock_actuel = stock_actuel - ? 
                WHERE id = ?
            """
            cursor.execute(update_stock_query, (quantite, item.get('product', {}).get('id')))
        
        db_manager.connection.commit()
        return vente_id
        
    except Exception as e:
        if db_manager.connection:
            db_manager.connection.rollback()
        raise e

def update_sale_status(db_manager: DatabaseManager, sale_id: int, new_status: str) -> bool:
    """Mettre à jour le statut d'une vente"""
    try:
        # S'assurer que la connexion est active
        if not db_manager.connection:
            if not db_manager.connect():
                print("❌ Impossible de se connecter à la base de données")
                return False
        
        query = "UPDATE ventes SET statut = ? WHERE id = ?"
        cursor = db_manager.connection.cursor()
        cursor.execute(query, (new_status, sale_id))
        db_manager.connection.commit()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour du statut: {e}")
        if db_manager.connection:
            db_manager.connection.rollback()
        return False

def get_sales_by_date_range(db_manager: DatabaseManager, start_date: str, end_date: str) -> List[Dict]:
    """Récupérer les ventes dans une période donnée"""
    return db_manager.execute_query("""
        SELECT v.*, 
               c.nom as client_nom, 
               c.prenom as client_prenom,
               c.entreprise as client_entreprise
        FROM ventes v
        LEFT JOIN clients c ON v.client_id = c.id
        WHERE DATE(v.date_vente) BETWEEN ? AND ?
        ORDER BY v.date_vente DESC
    """, (start_date, end_date))

def get_sales_statistics(db_manager: DatabaseManager, start_date: str = None, end_date: str = None) -> Dict:
    """Récupérer les statistiques de ventes"""
    where_clause = ""
    params = []
    
    if start_date and end_date:
        where_clause = "WHERE DATE(date_vente) BETWEEN ? AND ?"
        params = [start_date, end_date]
    
    query = f"""
        SELECT 
            COUNT(*) as nombre_ventes,
            SUM(montant_ttc) as chiffre_affaires,
            AVG(montant_ttc) as panier_moyen,
            SUM(CASE WHEN statut = 'Payée' THEN montant_ttc ELSE 0 END) as ca_paye,
            COUNT(DISTINCT client_id) as clients_actifs
        FROM ventes
        {where_clause}
    """
    
    results = db_manager.execute_query(query, tuple(params) if params else None)
    return results[0] if results else {}

def get_top_selling_products(db_manager: DatabaseManager, limit: int = 10, start_date: str = None, end_date: str = None) -> List[Dict]:
    """Récupérer les produits les plus vendus"""
    where_clause = ""
    params = []
    
    if start_date and end_date:
        where_clause = "WHERE DATE(v.date_vente) BETWEEN ? AND ?"
        params = [start_date, end_date]
    
    query = f"""
        SELECT 
            p.nom,
            p.reference,
            SUM(dv.quantite) as quantite_vendue,
            SUM(dv.montant_ligne) as ca_produit,
            AVG(dv.prix_unitaire) as prix_moyen
        FROM details_vente dv
        JOIN produits p ON dv.produit_id = p.id
        JOIN ventes v ON dv.vente_id = v.id
        {where_clause}
        GROUP BY p.id, p.nom, p.reference
        ORDER BY quantite_vendue DESC
        LIMIT ?
    """
    
    params.append(limit)
    return db_manager.execute_query(query, tuple(params))

# ==================== FONCTIONS CLIENTS ====================

def get_all_clients(db_manager: DatabaseManager) -> List[Dict]:
    """Récupérer tous les clients actifs"""
    return db_manager.execute_query("""
        SELECT * FROM clients 
        WHERE actif = 1 
        ORDER BY nom, prenom
    """)

def add_client(db_manager: DatabaseManager, client_data: Dict) -> int:
    """Ajouter un nouveau client"""
    query = """
        INSERT INTO clients (
            nom, prenom, entreprise, email, telephone, adresse, actif
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
    """
    
    params = (
        client_data.get('nom', ''),
        client_data.get('prenom', ''),
        client_data.get('entreprise', ''),
        client_data.get('email', ''),
        client_data.get('telephone', ''),
        client_data.get('adresse', ''),
        1 if client_data.get('actif', True) else 0
    )
    
    cursor = db_manager.connection.cursor()
    cursor.execute(query, params)
    db_manager.connection.commit()
    return cursor.lastrowid

# ==================== FONCTIONS CATÉGORIES ====================

def get_all_categories(db_manager: DatabaseManager) -> List[Dict]:
    """Récupérer toutes les catégories"""
    return db_manager.execute_query("""
        SELECT * FROM categories 
        ORDER BY nom
    """)

def add_category(db_manager: DatabaseManager, category_data: Dict) -> int:
    """Ajouter une nouvelle catégorie"""
    try:
        # S'assurer que la connexion est active
        if not db_manager.connection:
            db_manager.connect()
        
        query = """
            INSERT INTO categories (nom, description) 
            VALUES (?, ?)
        """
        
        params = (
            category_data.get('nom', ''),
            category_data.get('description', '')
        )
        
        cursor = db_manager.connection.cursor()
        cursor.execute(query, params)
        db_manager.connection.commit()
        return cursor.lastrowid
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout de la catégorie: {e}")
        if db_manager.connection:
            db_manager.connection.rollback()
        raise e

def update_category(db_manager: DatabaseManager, category_id: int, category_data: Dict) -> bool:
    """Mettre à jour une catégorie"""
    try:
        # S'assurer que la connexion est active
        if not db_manager.connection:
            db_manager.connect()
        
        query = """
            UPDATE categories 
            SET nom = ?, description = ?
            WHERE id = ?
        """
        
        params = (
            category_data.get('nom', ''),
            category_data.get('description', ''),
            category_id
        )
        
        cursor = db_manager.connection.cursor()
        cursor.execute(query, params)
        db_manager.connection.commit()
        return cursor.rowcount > 0
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour de la catégorie: {e}")
        if db_manager.connection:
            db_manager.connection.rollback()
        return False

def delete_category(db_manager: DatabaseManager, category_id: int) -> bool:
    """Supprimer une catégorie (si aucun produit ne l'utilise)"""
    try:
        # S'assurer que la connexion est active
        if not db_manager.connection:
            db_manager.connect()
        
        # Vérifier si des produits utilisent cette catégorie
        products_count = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM produits WHERE categorie_id = ?
        """, (category_id,))
        
        if products_count and products_count[0]['count'] > 0:
            print(f"❌ Impossible de supprimer la catégorie: {products_count[0]['count']} produit(s) l'utilisent")
            return False
        
        # Supprimer la catégorie
        query = "DELETE FROM categories WHERE id = ?"
        cursor = db_manager.connection.cursor()
        cursor.execute(query, (category_id,))
        db_manager.connection.commit()
        return cursor.rowcount > 0
        
    except Exception as e:
        print(f"❌ Erreur lors de la suppression de la catégorie: {e}")
        if db_manager.connection:
            db_manager.connection.rollback()
        return False

def get_category_by_id(db_manager: DatabaseManager, category_id: int) -> Dict:
    """Récupérer une catégorie par son ID"""
    categories = db_manager.execute_query("""
        SELECT * FROM categories WHERE id = ?
    """, (category_id,))
    
    return categories[0] if categories else {}