#!/usr/bin/env python3
"""
Test du formulaire optimisé de gestion des catégories
"""

import os
import sys

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.screenmanager import MDScreenManager
from database.db_manager import DatabaseManager
from screens.categories_optimized import CategoriesOptimizedScreen

class TestCategoriesApp(MDApp):
    def build(self):
        self.title = "Test Formulaire Catégories Optimisé"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        if not self.db_manager.connect():
            print("❌ Impossible de se connecter à la base de données")
            return
        
        if not self.db_manager.initialize_database():
            print("❌ Impossible d'initialiser la base de données")
            return
        
        print("✅ Base de données initialisée")
        
        # Créer le gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Écran principal de test
        main_screen = self.create_main_screen()
        self.screen_manager.add_widget(main_screen)
        
        # Écran de gestion des catégories optimisé
        categories_screen = CategoriesOptimizedScreen(name='categories')
        self.screen_manager.add_widget(categories_screen)
        
        return self.screen_manager
    
    def create_main_screen(self):
        """Créer l'écran principal de test"""
        screen = MDScreen(name='main')
        
        layout = MDBoxLayout(
            orientation='vertical',
            padding="20dp",
            spacing="20dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test Formulaire Catégories Optimisé",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        subtitle = MDLabel(
            text="Testez le nouveau formulaire de gestion des catégories avec toutes les optimisations",
            font_style="Body1",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Test écran catégories
        categories_btn = MDRaisedButton(
            text="📂 Ouvrir Gestion Catégories",
            size_hint_y=None,
            height="48dp",
            on_release=self.open_categories
        )
        
        # Test statistiques
        stats_btn = MDRaisedButton(
            text="📊 Afficher Statistiques",
            size_hint_y=None,
            height="48dp",
            on_release=self.show_stats
        )
        
        # Test données de test
        test_data_btn = MDRaisedButton(
            text="🧪 Créer Données de Test",
            size_hint_y=None,
            height="48dp",
            on_release=self.create_test_data
        )
        
        # Instructions
        instructions = MDLabel(
            text="""📋 Instructions de test :
            
1. Cliquez sur 'Ouvrir Gestion Catégories' pour tester l'interface
2. Testez la création de nouvelles catégories
3. Testez la modification des catégories existantes
4. Testez la suppression avec confirmation
5. Testez la recherche en temps réel
6. Observez les statistiques en temps réel

✨ Nouvelles fonctionnalités :
• Interface moderne avec cartes
• Recherche instantanée
• Statistiques en temps réel
• Validation avancée des formulaires
• Messages de confirmation/erreur
• Gestion des produits liés
• Performance optimisée""",
            font_style="Caption",
            theme_text_color="Secondary",
            text_size=(None, None)
        )
        
        buttons_layout.add_widget(categories_btn)
        buttons_layout.add_widget(stats_btn)
        buttons_layout.add_widget(test_data_btn)
        
        layout.add_widget(title)
        layout.add_widget(subtitle)
        layout.add_widget(buttons_layout)
        layout.add_widget(instructions)
        
        screen.add_widget(layout)
        return screen
    
    def open_categories(self, *args):
        """Ouvrir l'écran de gestion des catégories"""
        print("🔄 Ouverture de l'écran de gestion des catégories optimisé")
        self.screen_manager.current = 'categories'
    
    def show_stats(self, *args):
        """Afficher les statistiques des catégories"""
        try:
            if not self.db_manager.connect():
                print("❌ Impossible de se connecter à la base de données")
                return
            
            # Statistiques générales
            categories = self.db_manager.execute_query("""
                SELECT c.*, 
                       COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN produits p ON c.id = p.categorie_id AND p.actif = 1
                GROUP BY c.id, c.nom, c.description, c.date_creation
                ORDER BY products_count DESC, c.nom ASC
            """)
            
            if categories:
                print("\n📊 STATISTIQUES DES CATÉGORIES")
                print("=" * 50)
                
                total = len(categories)
                with_products = len([cat for cat in categories if cat.get('products_count', 0) > 0])
                empty = total - with_products
                total_products = sum(cat.get('products_count', 0) for cat in categories)
                
                print(f"📂 Total catégories: {total}")
                print(f"📦 Catégories avec produits: {with_products}")
                print(f"📭 Catégories vides: {empty}")
                print(f"🎯 Total produits liés: {total_products}")
                
                print("\n📋 DÉTAIL PAR CATÉGORIE:")
                print("-" * 50)
                
                for cat in categories:
                    products_count = cat.get('products_count', 0)
                    status = "📦" if products_count > 0 else "📭"
                    print(f"{status} {cat.get('nom', 'Sans nom')}: {products_count} produit(s)")
                
                print("\n✅ Statistiques affichées")
            else:
                print("📭 Aucune catégorie trouvée")
            
        except Exception as e:
            print(f"❌ Erreur lors de l'affichage des statistiques: {e}")
        
        finally:
            self.db_manager.disconnect()
    
    def create_test_data(self, *args):
        """Créer des données de test pour les catégories"""
        try:
            if not self.db_manager.connect():
                print("❌ Impossible de se connecter à la base de données")
                return
            
            print("🧪 Création de données de test...")
            
            # Catégories de test
            test_categories = [
                {
                    'nom': 'Électronique Test',
                    'description': 'Catégorie de test pour les produits électroniques'
                },
                {
                    'nom': 'Vêtements Test',
                    'description': 'Catégorie de test pour les vêtements et accessoires'
                },
                {
                    'nom': 'Maison Test',
                    'description': 'Catégorie de test pour les articles de maison'
                },
                {
                    'nom': 'Sport Test',
                    'description': 'Catégorie de test pour les articles de sport'
                },
                {
                    'nom': 'Livres Test',
                    'description': 'Catégorie de test pour les livres et médias'
                }
            ]
            
            created_count = 0
            
            for cat_data in test_categories:
                # Vérifier si la catégorie existe déjà
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (cat_data['nom'],)
                )
                
                if not existing:
                    # Créer la catégorie
                    cursor = self.db_manager.connection.cursor()
                    cursor.execute(
                        "INSERT INTO categories (nom, description) VALUES (?, ?)",
                        (cat_data['nom'], cat_data['description'])
                    )
                    self.db_manager.connection.commit()
                    
                    created_count += 1
                    print(f"✅ Catégorie créée: {cat_data['nom']}")
                else:
                    print(f"⚠️ Catégorie existe déjà: {cat_data['nom']}")
            
            print(f"\n🎉 {created_count} catégorie(s) de test créée(s)")
            
            # Créer quelques produits de test liés
            if created_count > 0:
                print("🔄 Création de produits de test liés...")
                
                # Récupérer les catégories créées
                categories = self.db_manager.execute_query(
                    "SELECT id, nom FROM categories WHERE nom LIKE '%Test%'"
                )
                
                if categories:
                    test_products = [
                        {
                            'nom': 'Smartphone Test',
                            'reference': 'SMART-TEST-001',
                            'prix_vente': 299.99,
                            'categorie_id': categories[0]['id'] if len(categories) > 0 else None
                        },
                        {
                            'nom': 'T-shirt Test',
                            'reference': 'TSHIRT-TEST-001',
                            'prix_vente': 19.99,
                            'categorie_id': categories[1]['id'] if len(categories) > 1 else None
                        },
                        {
                            'nom': 'Lampe Test',
                            'reference': 'LAMP-TEST-001',
                            'prix_vente': 49.99,
                            'categorie_id': categories[2]['id'] if len(categories) > 2 else None
                        }
                    ]
                    
                    products_created = 0
                    
                    for prod_data in test_products:
                        if prod_data['categorie_id']:
                            # Vérifier si le produit existe déjà
                            existing = self.db_manager.execute_query(
                                "SELECT id FROM produits WHERE reference = ?",
                                (prod_data['reference'],)
                            )
                            
                            if not existing:
                                cursor = self.db_manager.connection.cursor()
                                cursor.execute(
                                    """INSERT INTO produits (nom, reference, prix_vente, categorie_id) 
                                       VALUES (?, ?, ?, ?)""",
                                    (prod_data['nom'], prod_data['reference'], 
                                     prod_data['prix_vente'], prod_data['categorie_id'])
                                )
                                self.db_manager.connection.commit()
                                
                                products_created += 1
                                print(f"✅ Produit créé: {prod_data['nom']}")
                    
                    print(f"🎉 {products_created} produit(s) de test créé(s)")
            
            print("\n✅ Données de test créées avec succès")
            print("🔄 Vous pouvez maintenant tester l'interface avec des données réelles")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des données de test: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            self.db_manager.disconnect()

if __name__ == "__main__":
    print("🧪 LANCEMENT DU TEST FORMULAIRE CATÉGORIES OPTIMISÉ")
    print("=" * 60)
    print("🎯 Ce test permet de valider toutes les optimisations")
    print("📋 Interface moderne avec cartes et statistiques")
    print("🔍 Recherche instantanée et validation avancée")
    print("⚡ Performance optimisée avec chargement asynchrone")
    print("=" * 60)
    
    try:
        app = TestCategoriesApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("Appuyez sur Entrée pour quitter...")