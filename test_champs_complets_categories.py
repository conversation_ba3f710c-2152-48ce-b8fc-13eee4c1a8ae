#!/usr/bin/env python3
"""
Test pour vérifier que tous les champs du formulaire catégorie sont visibles
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoryFormDialog


class TestCompleteCategoryFormApp(MDApp):
    """Application de test pour le formulaire complet de catégorie"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Formulaire Complet Catégorie"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test - Formulaire Complet Catégorie",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Testez le formulaire avec tous les champs :\n"
                 "• Nom (obligatoire)\n"
                 "• Description (optionnelle)\n"
                 "• ID (lecture seule)\n"
                 "• Date de création (lecture seule)\n"
                 "• Nombre de produits liés (lecture seule)",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="150dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="200dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="➕ Nouvelle Catégorie (Tous les champs)",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_new_complete
        )
        
        # Test modification avec données
        edit_btn = MDRaisedButton(
            text="✏️ Modifier Catégorie (Avec données)",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_edit_complete
        )
        
        # Test avec beaucoup de produits
        products_btn = MDRaisedButton(
            text="📦 Catégorie avec Produits",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_with_products
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(products_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Cliquez sur un bouton pour tester le formulaire complet.\n"
                 "Vérifiez que tous les champs sont visibles et correctement formatés.",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_complete(self, *args):
        """Tester le formulaire pour nouvelle catégorie"""
        print("🧪 Test: Nouveau formulaire complet")
        self.result_label.text = "Formulaire NOUVEAU ouvert.\n" \
                                "Vérifiez :\n" \
                                "• Champ Nom (vide, modifiable)\n" \
                                "• Champ Description (vide, modifiable)\n" \
                                "• ID = 'Nouveau' (lecture seule)\n" \
                                "• Date = aujourd'hui (lecture seule)\n" \
                                "• Produits = 'Aucun produit lié' (lecture seule)"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
    
    def test_edit_complete(self, *args):
        """Tester le formulaire de modification avec données complètes"""
        print("🧪 Test: Formulaire de modification complet")
        self.result_label.text = "Formulaire MODIFICATION ouvert.\n" \
                                "Vérifiez :\n" \
                                "• Champ Nom (pré-rempli, modifiable)\n" \
                                "• Champ Description (pré-rempli, modifiable)\n" \
                                "• ID = '1' (lecture seule)\n" \
                                "• Date formatée (lecture seule)\n" \
                                "• Produits = '3 produit(s) lié(s)' (lecture seule)"
        
        # Données de test complètes
        test_data = {
            'id': 1,
            'nom': 'Électronique',
            'description': 'Produits électroniques et informatiques de haute technologie',
            'products_count': 3,
            'date_creation': '2024-01-15T10:30:00'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
    
    def test_with_products(self, *args):
        """Tester avec beaucoup de produits liés"""
        print("🧪 Test: Catégorie avec beaucoup de produits")
        self.result_label.text = "Formulaire avec BEAUCOUP DE PRODUITS.\n" \
                                "Vérifiez :\n" \
                                "• Champ Nom (pré-rempli)\n" \
                                "• Champ Description (longue)\n" \
                                "• ID = '5' (lecture seule)\n" \
                                "• Date ancienne (lecture seule)\n" \
                                "• Produits = '25 produit(s) lié(s)' (lecture seule)"
        
        # Données avec beaucoup de produits
        test_data = {
            'id': 5,
            'nom': 'Vêtements',
            'description': 'Large gamme de vêtements pour hommes, femmes et enfants. Incluant les accessoires, chaussures, et articles de mode saisonniers.',
            'products_count': 25,
            'date_creation': '2023-06-20T14:45:30'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_callback
        )
        dialog.open()
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        cat_id = category_data.get('id', 'Nouveau')
        
        self.result_label.text = f"✅ SUCCÈS - Catégorie sauvegardée !\n" \
                                f"ID: {cat_id}\n" \
                                f"Nom: {nom}\n" \
                                f"Description: {description[:50]}{'...' if len(description) > 50 else ''}"
        
        print("🎉 SUCCÈS - Catégorie sauvegardée:")
        print(f"  - ID: {cat_id}")
        print(f"  - Nom: {nom}")
        print(f"  - Description: {description}")


def main():
    """Fonction principale"""
    print("🧪 Test du Formulaire Complet - Catégories")
    print("=" * 60)
    print("Ce test vérifie que TOUS les champs sont visibles :")
    print("1. ✅ Nom de la catégorie (modifiable)")
    print("2. ✅ Description (modifiable)")
    print("3. ✅ ID de la catégorie (lecture seule)")
    print("4. ✅ Date de création (lecture seule)")
    print("5. ✅ Nombre de produits liés (lecture seule)")
    print("=" * 60)
    print("Testez chaque scénario et vérifiez l'affichage !")
    print("=" * 60)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestCompleteCategoryFormApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()