"""
Test du formulaire de catégories corrigé
"""

import os
import sys
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.insert(0, str(Path(__file__).parent))

from kivy.app import App
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from new_category_form import CategoryFormDialog


class TestCategoryFormApp(MDApp):
    """Application de test pour le formulaire de catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Formulaire Catégories - Corrigé"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construire l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="40dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test du Formulaire de Catégories Corrigé",
            font_style="H5",
            theme_text_color="Primary",
            size_hint_y=None,
            height="60dp",
            halign="center"
        )
        
        # Description
        description = MDLabel(
            text="Cliquez sur les boutons pour tester le formulaire d'ajout et de modification des catégories.",
            font_style="Body1",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        # Bouton pour nouveau formulaire
        new_btn = MDRaisedButton(
            text="➕ Nouveau Formulaire Catégorie",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.open_new_category_form
        )
        
        # Bouton pour formulaire d'édition
        edit_btn = MDRaisedButton(
            text="✏️ Formulaire Édition Catégorie",
            size_hint=(None, None),
            size=("300dp", "50dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.open_edit_category_form
        )
        
        # Statut
        self.status_label = MDLabel(
            text="✅ Prêt pour les tests",
            font_style="Body2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(description)
        layout.add_widget(new_btn)
        layout.add_widget(edit_btn)
        layout.add_widget(self.status_label)
        
        screen.add_widget(layout)
        return screen
    
    def open_new_category_form(self, *args):
        """Ouvrir le formulaire de nouvelle catégorie"""
        try:
            self.status_label.text = "🔄 Ouverture du formulaire de création..."
            dialog = CategoryFormDialog(
                on_save_callback=self.on_category_saved
            )
            dialog.open()
            self.status_label.text = "📝 Formulaire de création ouvert"
        except Exception as e:
            self.status_label.text = f"❌ Erreur: {str(e)}"
            print(f"Erreur lors de l'ouverture du formulaire: {e}")
    
    def open_edit_category_form(self, *args):
        """Ouvrir le formulaire d'édition avec des données de test"""
        try:
            self.status_label.text = "🔄 Ouverture du formulaire d'édition..."
            
            # Données de test pour l'édition
            test_data = {
                'id': 1,
                'nom': 'Électronique',
                'description': 'Produits électroniques et informatiques'
            }
            
            dialog = CategoryFormDialog(
                category_data=test_data,
                on_save_callback=self.on_category_saved
            )
            dialog.open()
            self.status_label.text = "✏️ Formulaire d'édition ouvert"
        except Exception as e:
            self.status_label.text = f"❌ Erreur: {str(e)}"
            print(f"Erreur lors de l'ouverture du formulaire: {e}")
    
    def on_category_saved(self, category_data):
        """Callback appelé quand une catégorie est sauvegardée"""
        try:
            nom = category_data.get('nom', 'Sans nom')
            self.status_label.text = f"✅ Catégorie '{nom}' sauvegardée avec succès!"
            print(f"Catégorie sauvegardée: {category_data}")
        except Exception as e:
            self.status_label.text = f"❌ Erreur callback: {str(e)}"
            print(f"Erreur dans le callback: {e}")


if __name__ == '__main__':
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    # Lancement de l'application de test
    TestCategoryFormApp().run()
