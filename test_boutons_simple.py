#!/usr/bin/env python3
"""
Test simple pour vérifier le fonctionnement des boutons du formulaire catégorie
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoryFormDialog


class TestSimpleButtonsApp(MDApp):
    """Application de test simple pour les boutons"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Simple - Boutons Catégorie"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="30dp",
            padding="30dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test Simple - Boutons Formulaire",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="80dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Cliquez sur le bouton ci-dessous pour ouvrir le formulaire.\n"
                 "Testez les boutons 'Annuler' et 'Enregistrer'.\n"
                 "Les messages s'afficheront dans la console.",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="100dp"
        )
        
        # Bouton de test
        test_btn = MDRaisedButton(
            text="🚀 Ouvrir Formulaire Catégorie",
            size_hint=(None, None),
            size=("300dp", "60dp"),
            pos_hint={'center_x': 0.5},
            on_release=self.open_form
        )
        
        # Résultats
        self.result_label = MDLabel(
            text="Résultats s'afficheront ici...",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(test_btn)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def open_form(self, *args):
        """Ouvrir le formulaire de test"""
        print("🧪 Ouverture du formulaire de catégorie...")
        self.result_label.text = "Formulaire ouvert ! Testez les boutons."
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_callback
        )
        dialog.open()
        print("✅ Formulaire ouvert avec succès")
    
    def on_save_callback(self, category_data):
        """Callback de sauvegarde"""
        nom = category_data.get('nom', 'Sans nom')
        description = category_data.get('description', 'Aucune')
        
        message = f"✅ SUCCÈS !\nNom: {nom}\nDescription: {description}"
        self.result_label.text = message
        
        print("🎉 SUCCÈS - Catégorie sauvegardée:")
        print(f"  - Nom: {nom}")
        print(f"  - Description: {description}")
        print(f"  - ID: {category_data.get('id', 'Nouveau')}")


def main():
    """Fonction principale"""
    print("🧪 Test Simple - Boutons Formulaire Catégorie")
    print("=" * 50)
    print("Ce test va ouvrir un formulaire de catégorie.")
    print("Testez les fonctionnalités suivantes:")
    print("1. Bouton 'Annuler' - doit fermer le formulaire")
    print("2. Bouton 'Enregistrer' - doit valider et sauvegarder")
    print("3. Validation - testez avec nom vide")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestSimpleButtonsApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()