#!/usr/bin/env python3
"""
Test spécifique pour vérifier le fonctionnement des boutons du formulaire catégorie
"""

import os
import sys
import warnings

# Supprimer l'avertissement spécifique de KivyMD 1.2.0
warnings.filterwarnings("ignore", message=".*width_mult.*", category=UserWarning)

# Configurer le logger de Kivy pour ignorer les avertissements de dépréciation
os.environ['KIVY_LOG_MODE'] = 'PYTHON'
import logging
logging.getLogger('kivy').setLevel(logging.ERROR)

from kivy.app import App
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel
from screens.categories_screen import CategoryFormDialog


class TestButtonsCategoryApp(MDApp):
    """Application de test pour les boutons du formulaire de catégorie"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test - Boutons Formulaire Catégorie"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.test_results = []
    
    def build(self):
        """Construction de l'interface de test"""
        screen = MDScreen()
        
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp"
        )
        
        # Titre
        title = MDLabel(
            text="🧪 Test des Boutons - Formulaire Catégorie",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        # Instructions
        instructions = MDLabel(
            text="Testez les boutons du formulaire :\n"
                 "1. Cliquez sur 'Tester Formulaire'\n"
                 "2. Vérifiez que les boutons 'Annuler' et 'Enregistrer' fonctionnent\n"
                 "3. Testez la validation et la sauvegarde",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="120dp"
        )
        
        # Boutons de test
        buttons_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp",
            size_hint_y=None,
            height="300dp"
        )
        
        # Test nouveau formulaire
        new_btn = MDRaisedButton(
            text="➕ Tester Nouveau Formulaire",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_new_form
        )
        
        # Test modification formulaire
        edit_btn = MDRaisedButton(
            text="✏️ Tester Modification",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_edit_form
        )
        
        # Test validation
        validation_btn = MDRaisedButton(
            text="🔍 Tester Validation",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_validation
        )
        
        # Test annulation
        cancel_btn = MDRaisedButton(
            text="❌ Tester Annulation",
            size_hint_y=None,
            height="50dp",
            on_release=self.test_cancel
        )
        
        buttons_layout.add_widget(new_btn)
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(validation_btn)
        buttons_layout.add_widget(cancel_btn)
        
        # Résultats
        self.result_label = MDLabel(
            text="Résultats des tests s'afficheront ici...\n"
                 "Cliquez sur les boutons ci-dessus pour commencer.",
            font_style="Body2",
            theme_text_color="Secondary",
            halign="center"
        )
        
        layout.add_widget(title)
        layout.add_widget(instructions)
        layout.add_widget(buttons_layout)
        layout.add_widget(self.result_label)
        
        screen.add_widget(layout)
        return screen
    
    def test_new_form(self, *args):
        """Tester le formulaire de nouvelle catégorie"""
        self.result_label.text = "🧪 Test: Nouveau formulaire ouvert\n" \
                                "Vérifiez que les boutons 'Annuler' et 'Enregistrer' sont visibles et cliquables"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_success
        )
        dialog.open()
    
    def test_edit_form(self, *args):
        """Tester le formulaire de modification"""
        self.result_label.text = "🧪 Test: Formulaire de modification ouvert\n" \
                                "Données pré-remplies. Testez les boutons."
        
        # Données de test
        test_data = {
            'id': 1,
            'nom': 'Électronique',
            'description': 'Produits électroniques et informatiques',
            'products_count': 5,
            'date_creation': '2024-01-15'
        }
        
        dialog = CategoryFormDialog(
            category_data=test_data,
            on_save_callback=self.on_save_success
        )
        dialog.open()
    
    def test_validation(self, *args):
        """Tester la validation avec données vides"""
        self.result_label.text = "🧪 Test: Formulaire vide pour tester la validation\n" \
                                "Cliquez 'Enregistrer' sans saisir de nom pour tester la validation"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_success
        )
        dialog.open()
    
    def test_cancel(self, *args):
        """Tester l'annulation"""
        self.result_label.text = "🧪 Test: Test d'annulation\n" \
                                "Saisissez des données puis cliquez 'Annuler' pour tester"
        
        dialog = CategoryFormDialog(
            on_save_callback=self.on_save_success
        )
        dialog.open()
    
    def on_save_success(self, category_data):
        """Callback de succès"""
        nom = category_data.get('nom', 'Sans nom')
        self.result_label.text = f"✅ SUCCÈS: Catégorie sauvegardée\n" \
                                f"Nom: {nom}\n" \
                                f"Description: {category_data.get('description', 'Aucune')}\n" \
                                f"ID: {category_data.get('id', 'Nouveau')}\n\n" \
                                f"🎉 Les boutons fonctionnent correctement !"
        
        print(f"✅ Catégorie sauvegardée avec succès: {category_data}")
        self.test_results.append(f"Succès: {nom}")
    
    def on_stop(self):
        """Afficher les résultats à la fermeture"""
        if self.test_results:
            print("\n🧪 RÉSULTATS DES TESTS:")
            print("=" * 40)
            for result in self.test_results:
                print(f"  ✅ {result}")
            print(f"\n📊 Total: {len(self.test_results)} test(s) réussi(s)")
        else:
            print("\n📝 Aucun test de sauvegarde effectué")


def main():
    """Fonction principale"""
    print("🧪 Test des Boutons - Formulaire Catégorie")
    print("=" * 50)
    print("Instructions:")
    print("1. Cliquez sur les boutons de test")
    print("2. Dans chaque formulaire, testez:")
    print("   - Bouton 'Annuler' (doit fermer le formulaire)")
    print("   - Bouton 'Enregistrer' (doit valider et sauvegarder)")
    print("3. Vérifiez les messages d'erreur/succès")
    print("=" * 50)
    
    # Configuration pour Windows
    if sys.platform == 'win32':
        os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'
    
    try:
        app = TestButtonsCategoryApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()