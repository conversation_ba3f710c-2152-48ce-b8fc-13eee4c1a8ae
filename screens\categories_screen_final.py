"""
Écran de gestion des catégories - Version finale corrigée
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.snackbar import MDSnackbar
from kivymd.app import MDApp
from kivy.clock import Clock
from kivy.metrics import dp
import threading
from database.db_manager import DatabaseManager


class CategoryCard(MDCard):
    """Carte optimisée pour afficher une catégorie"""
    
    def __init__(self, category_data, on_edit_callback=None, on_delete_callback=None, **kwargs):
        super().__init__(**kwargs)
        self.category_data = category_data
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        
        # Configuration de la carte
        self.orientation = 'vertical'
        self.size_hint_y = None
        self.height = "140dp"
        self.padding = "12dp"
        self.spacing = "8dp"
        self.elevation = 2
        self.radius = [8]
        
        self.create_content()
    
    def create_content(self):
        """Créer le contenu de la carte"""
        layout = MDBoxLayout(
            orientation='vertical',
            spacing="8dp"
        )
        
        # En-tête avec nom et boutons
        header_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="40dp",
            spacing="8dp"
        )
        
        # Nom de la catégorie
        name_label = MDLabel(
            text=f"📂 {self.category_data.get('nom', 'Sans nom')}",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_x=0.7
        )
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_x=0.3,
            spacing="4dp"
        )
        
        edit_btn = MDIconButton(
            icon="pencil",
            theme_icon_color="Custom",
            icon_color=[0.2, 0.6, 1, 1],
            on_release=lambda x: self.edit_category()
        )
        
        delete_btn = MDIconButton(
            icon="delete",
            theme_icon_color="Custom",
            icon_color=[1, 0.3, 0.3, 1],
            on_release=lambda x: self.delete_category()
        )
        
        buttons_layout.add_widget(edit_btn)
        buttons_layout.add_widget(delete_btn)
        
        header_layout.add_widget(name_label)
        header_layout.add_widget(buttons_layout)
        
        # Description
        description = self.category_data.get('description', 'Aucune description')
        if len(description) > 80:
            description = description[:80] + "..."
        
        description_label = MDLabel(
            text=description,
            font_style="Body2",
            theme_text_color="Secondary",
            size_hint_y=None,
            height="40dp"
        )
        
        # Statistiques
        stats_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height="30dp",
            spacing="16dp"
        )
        
        products_count = self.category_data.get('products_count', 0)
        products_label = MDLabel(
            text=f"📦 {products_count} produit(s)",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        # Date de création
        date_creation = self.category_data.get('date_creation', '')
        if date_creation:
            try:
                from datetime import datetime
                if isinstance(date_creation, str):
                    date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                    date_formatted = date_obj.strftime('%d/%m/%Y')
                else:
                    date_formatted = str(date_creation)[:10]
            except:
                date_formatted = str(date_creation)[:10]
        else:
            date_formatted = "Non définie"
        
        date_label = MDLabel(
            text=f"📅 {date_formatted}",
            font_style="Caption",
            theme_text_color="Secondary",
            size_hint_x=0.5
        )
        
        stats_layout.add_widget(products_label)
        stats_layout.add_widget(date_label)
        
        layout.add_widget(header_layout)
        layout.add_widget(description_label)
        layout.add_widget(stats_layout)
        
        self.add_widget(layout)
    
    def edit_category(self):
        """Éditer la catégorie"""
        if self.on_edit_callback:
            self.on_edit_callback(self.category_data)
    
    def delete_category(self):
        """Supprimer la catégorie"""
        if self.on_delete_callback:
            self.on_delete_callback(self.category_data)


class CategoryFormDialog(MDDialog):
    """Formulaire de catégorie corrigé avec champs visibles"""
    
    def __init__(self, category_data=None, on_save_callback=None, **kwargs):
        self.category_data = category_data or {}
        self.on_save_callback = on_save_callback
        self.db_manager = DatabaseManager()
        
        # Créer les boutons d'abord
        self.cancel_btn = MDFlatButton(
            text="❌ Annuler",
            on_release=self.dismiss_dialog
        )
        
        self.save_btn = MDRaisedButton(
            text="💾 Enregistrer",
            on_release=self.save_category
        )
        
        super().__init__(
            title="✏️ Modifier la catégorie" if category_data else "➕ Nouvelle catégorie",
            type="custom",
            size_hint=(0.9, None),
            height="600dp",
            buttons=[self.cancel_btn, self.save_btn],
            **kwargs
        )
        
        self.create_form()
    
    def create_form(self):
        """Créer le formulaire avec champs garantis visibles"""
        # Container principal
        main_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            padding="20dp",
            size_hint_y=None,
            height="480dp"
        )
        
        # Titre du formulaire
        title_label = MDLabel(
            text="📝 Informations de la catégorie",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height="40dp",
            halign="left"
        )
        main_container.add_widget(title_label)
        
        # Container pour les champs
        fields_container = MDBoxLayout(
            orientation='vertical',
            spacing="20dp",
            size_hint_y=None,
            height="400dp"
        )
        
        # Champ nom avec label séparé
        nom_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="80dp"
        )
        
        nom_label = MDLabel(
            text="📂 Nom de la catégorie *",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.nom_field = MDTextField(
            text=self.category_data.get('nom', ''),
            hint_text="Saisissez le nom de la catégorie",
            size_hint_y=None,
            height="56dp",
            mode="rectangle",
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            required=True,
            max_text_length=100
        )
        
        nom_container.add_widget(nom_label)
        nom_container.add_widget(self.nom_field)
        
        # Champ description avec label séparé
        desc_container = MDBoxLayout(
            orientation='vertical',
            spacing="8dp",
            size_hint_y=None,
            height="120dp"
        )
        
        desc_label = MDLabel(
            text="📝 Description (optionnelle)",
            font_style="Subtitle2",
            theme_text_color="Primary",
            size_hint_y=None,
            height="24dp"
        )
        
        self.description_field = MDTextField(
            text=self.category_data.get('description', ''),
            hint_text="Description détaillée de la catégorie",
            multiline=True,
            size_hint_y=None,
            height="88dp",
            mode="rectangle",
            line_color_normal=[0.2, 0.2, 0.2, 1],
            line_color_focus=[0.1, 0.5, 0.8, 1],
            text_color_normal=[0, 0, 0, 1],
            text_color_focus=[0, 0, 0, 1],
            hint_text_color_normal=[0.4, 0.4, 0.4, 1],
            hint_text_color_focus=[0.1, 0.5, 0.8, 1],
            fill_color_normal=[0.95, 0.95, 0.95, 1],
            fill_color_focus=[0.98, 0.98, 0.98, 1],
            max_text_length=500
        )
        
        desc_container.add_widget(desc_label)
        desc_container.add_widget(self.description_field)
        
        fields_container.add_widget(nom_container)
        fields_container.add_widget(desc_container)
        
        # Informations supplémentaires si modification
        if self.category_data and self.category_data.get('id'):
            info_container = MDBoxLayout(
                orientation='vertical',
                spacing="8dp",
                size_hint_y=None,
                height="100dp"
            )
            
            # Ligne 1: ID et nombre de produits
            info_row1 = MDBoxLayout(
                orientation='horizontal',
                spacing="16dp",
                size_hint_y=None,
                height="30dp"
            )
            
            id_label = MDLabel(
                text=f"🆔 ID: {self.category_data.get('id', 'N/A')}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            
            products_count = self.category_data.get('products_count', 0)
            products_label = MDLabel(
                text=f"📦 {products_count} produit(s) lié(s)",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_x=0.5
            )
            
            info_row1.add_widget(id_label)
            info_row1.add_widget(products_label)
            
            # Ligne 2: Date de création
            date_creation = self.category_data.get('date_creation', '')
            if date_creation:
                try:
                    from datetime import datetime
                    if isinstance(date_creation, str):
                        date_obj = datetime.fromisoformat(date_creation.replace('Z', '+00:00'))
                        date_formatted = date_obj.strftime('%d/%m/%Y à %H:%M')
                    else:
                        date_formatted = str(date_creation)[:16]
                except:
                    date_formatted = str(date_creation)[:16]
            else:
                date_formatted = "Non définie"
            
            date_label = MDLabel(
                text=f"📅 Créée le: {date_formatted}",
                font_style="Caption",
                theme_text_color="Secondary",
                size_hint_y=None,
                height="30dp"
            )
            
            info_container.add_widget(info_row1)
            info_container.add_widget(date_label)
            fields_container.add_widget(info_container)
        
        main_container.add_widget(fields_container)
        
        # IMPORTANT: Assigner le contenu au dialog
        self.content_cls = main_container
    
    def save_category(self, *args):
        """Sauvegarder la catégorie avec validation"""
        nom = self.nom_field.text.strip()
        if not nom:
            self.show_error("Le nom de la catégorie est obligatoire")
            return
        
        if len(nom) < 2:
            self.show_error("Le nom doit contenir au moins 2 caractères")
            return
        
        description = self.description_field.text.strip()
        
        try:
            if not self.db_manager.connect():
                self.show_error("Impossible de se connecter à la base de données")
                return
            
            if self.category_data and self.category_data.get('id'):  # Modification
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ? AND id != ?",
                    (nom, self.category_data['id'])
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                success = self.db_manager.execute_update(
                    "UPDATE categories SET nom = ?, description = ? WHERE id = ?",
                    (nom, description, self.category_data['id'])
                )
                
                if success:
                    self.show_success("Catégorie modifiée avec succès")
                    category_data = {
                        'id': self.category_data['id'],
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la modification")
                    return
            
            else:  # Création
                existing = self.db_manager.execute_query(
                    "SELECT id FROM categories WHERE nom = ?",
                    (nom,)
                )
                
                if existing:
                    self.show_error(f"Une catégorie avec le nom '{nom}' existe déjà")
                    return
                
                category_id = self.db_manager.execute_insert(
                    "INSERT INTO categories (nom, description) VALUES (?, ?)",
                    (nom, description)
                )
                
                if category_id:
                    self.show_success("Catégorie créée avec succès")
                    category_data = {
                        'id': category_id,
                        'nom': nom,
                        'description': description
                    }
                else:
                    self.show_error("Erreur lors de la création")
                    return
            
            if self.on_save_callback:
                self.on_save_callback(category_data)
            
            self.dismiss()
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {str(e)}")
        
        finally:
            self.db_manager.close()
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"❌ {message}")
    
    def show_success(self, message):
        """Afficher un message de succès"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"✅ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"✅ {message}")
    
    def dismiss_dialog(self, *args):
        """Fermer le dialog"""
        self.dismiss()


class CategoriesScreen(MDScreen):
    """Écran optimisé de gestion des catégories"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db_manager = DatabaseManager()
        self.categories_data = []
        self.create_interface()
        self.load_categories()
    
    def create_interface(self):
        """Créer l'interface optimisée"""
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Barre d'outils
        toolbar = MDTopAppBar(
            title="📂 Gestion des Catégories",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[
                ["plus", lambda x: self.add_category()],
                ["refresh", lambda x: self.refresh_categories()]
            ],
            elevation=2
        )
        
        # Zone de contenu avec scroll
        scroll = MDScrollView()
        
        self.categories_layout = MDBoxLayout(
            orientation='vertical',
            spacing="12dp",
            padding="16dp",
            size_hint_y=None
        )
        self.categories_layout.bind(minimum_height=self.categories_layout.setter('height'))
        
        scroll.add_widget(self.categories_layout)
        
        main_layout.add_widget(toolbar)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def load_categories(self):
        """Charger les catégories depuis la base de données"""
        def load_in_background():
            try:
                if not self.db_manager.connect():
                    Clock.schedule_once(lambda dt: self.show_error("Impossible de se connecter à la base de données"))
                    return
                
                # Requête avec comptage des produits
                query = """
                SELECT c.id, c.nom, c.description, c.date_creation,
                       COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN products p ON c.id = p.category_id
                GROUP BY c.id, c.nom, c.description, c.date_creation
                ORDER BY c.nom
                """
                
                categories = self.db_manager.execute_query(query)
                
                if categories:
                    self.categories_data = []
                    for cat in categories:
                        category_dict = {
                            'id': cat[0],
                            'nom': cat[1],
                            'description': cat[2] or '',
                            'date_creation': cat[3],
                            'products_count': cat[4]
                        }
                        self.categories_data.append(category_dict)
                    
                    Clock.schedule_once(lambda dt: self.display_categories())
                else:
                    self.categories_data = []
                    Clock.schedule_once(lambda dt: self.display_empty_state())
                
            except Exception as e:
                Clock.schedule_once(lambda dt: self.show_error(f"Erreur lors du chargement: {str(e)}"))
            finally:
                self.db_manager.close()
        
        # Lancer en arrière-plan
        threading.Thread(target=load_in_background, daemon=True).start()
    
    def display_categories(self):
        """Afficher les catégories dans l'interface"""
        self.categories_layout.clear_widgets()
        
        if not self.categories_data:
            self.display_empty_state()
            return
        
        for category in self.categories_data:
            card = CategoryCard(
                category_data=category,
                on_edit_callback=self.edit_category,
                on_delete_callback=self.delete_category
            )
            self.categories_layout.add_widget(card)
        
        print(f"📂 {len(self.categories_data)} catégorie(s) affichée(s)")
    
    def display_empty_state(self):
        """Afficher l'état vide"""
        self.categories_layout.clear_widgets()
        
        empty_card = MDCard(
            size_hint_y=None,
            height="200dp",
            padding="20dp",
            elevation=1,
            radius=[8]
        )
        
        empty_layout = MDBoxLayout(
            orientation='vertical',
            spacing="16dp"
        )
        
        empty_icon = MDLabel(
            text="📂",
            font_style="H2",
            halign="center",
            size_hint_y=None,
            height="60dp"
        )
        
        empty_title = MDLabel(
            text="Aucune catégorie",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        empty_subtitle = MDLabel(
            text="Cliquez sur le bouton + pour créer votre première catégorie",
            font_style="Body1",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        add_btn = MDRaisedButton(
            text="➕ Créer une catégorie",
            size_hint=(None, None),
            size=("200dp", "40dp"),
            pos_hint={'center_x': 0.5},
            on_release=lambda x: self.add_category()
        )
        
        empty_layout.add_widget(empty_icon)
        empty_layout.add_widget(empty_title)
        empty_layout.add_widget(empty_subtitle)
        empty_layout.add_widget(add_btn)
        
        empty_card.add_widget(empty_layout)
        self.categories_layout.add_widget(empty_card)
    
    def add_category(self, *args):
        """Ajouter une nouvelle catégorie"""
        dialog = CategoryFormDialog(
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def edit_category(self, category_data):
        """Éditer une catégorie"""
        dialog = CategoryFormDialog(
            category_data=category_data,
            on_save_callback=self.on_category_saved
        )
        dialog.open()
    
    def delete_category(self, category_data):
        """Supprimer une catégorie"""
        # TODO: Implémenter la suppression avec confirmation
        self.show_error("Suppression non implémentée")
    
    def on_category_saved(self, category_data):
        """Callback appelé quand une catégorie est sauvegardée"""
        self.refresh_categories()
    
    def refresh_categories(self, *args):
        """Actualiser la liste des catégories"""
        self.load_categories()
    
    def go_back(self, *args):
        """Retourner à l'écran précédent"""
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = 'dashboard'
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        try:
            snackbar = MDSnackbar(
                MDLabel(
                    text=f"❌ {message}",
                    theme_text_color="Custom",
                    text_color=(1, 1, 1, 1)
                ),
                y="24dp",
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            print(f"Erreur Snackbar: {e}")
            print(f"❌ {message}")